#!/usr/bin/env python3
"""
Script test độ chính xác OCR sau khi cải thiện
"""

import requests
import json
import time
from PIL import Image, ImageDraw, ImageFont
import numpy as np
import io
import base64

def create_test_prescription_image():
    """
    Tạo ảnh đơn thuốc test
    """
    # Tạo ảnh trắng
    img = Image.new('RGB', (600, 800), color='white')
    draw = ImageDraw.Draw(img)
    
    # Thêm text mẫu đơn thuốc
    test_text = [
        "BỆNH VIỆN ĐA KHOA TRUNG ƯƠNG",
        "Địa chỉ: 123 Đường ABC, Hà Nội",
        "Tel: 024-1234567",
        "",
        "ĐỚN THUỐC",
        "",
        "Bệnh nhân: Nguyễn Văn A",
        "Tuổi: 35    Giới tính: Nam",
        "Địa chỉ: 456 Đường XYZ, <PERSON>à Nội",
        "",
        "Chẩn đoán: <PERSON><PERSON><PERSON> cúm",
        "",
        "Thuốc:",
        "1. Paracetamol 500mg - 2 viên x 3 lần/ngày",
        "2. Amoxicillin 250mg - 1 viên x 2 lần/ngày",
        "3. Vitamin C 100mg - 1 viên x 1 lần/ngày",
        "",
        "Hướng dẫn sử dụng:",
        "- Uống sau ăn",
        "- Uống đủ nước",
        "- Tái khám sau 3 ngày",
        "",
        "Bác sĩ: BS. Trần Thị B",
        "Ngày: 17/07/2025"
    ]
    
    y_position = 50
    for line in test_text:
        if line:  # Không vẽ dòng trống
            draw.text((50, y_position), line, fill='black')
        y_position += 25
    
    return img

def test_ocr_api(image):
    """
    Test API OCR với ảnh
    """
    # Convert PIL image to bytes
    img_byte_arr = io.BytesIO()
    image.save(img_byte_arr, format='PNG')
    img_byte_arr = img_byte_arr.getvalue()
    
    # Gửi request đến API
    files = {'file': ('test_prescription.png', img_byte_arr, 'image/png')}
    
    try:
        response = requests.post('http://localhost:8000/extract-text', files=files, timeout=60)
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"API Error: {response.status_code}")
            print(response.text)
            return None
            
    except Exception as e:
        print(f"Request error: {e}")
        return None

def analyze_results(result):
    """
    Phân tích kết quả OCR
    """
    if not result:
        print("❌ Không có kết quả")
        return
    
    print("🔍 PHÂN TÍCH KẾT QUẢ OCR")
    print("=" * 60)
    
    # Tesseract results
    tesseract_text = result.get('tesseract_result', '')
    tesseract_length = len(tesseract_text)
    
    print(f"📝 TESSERACT OCR:")
    print(f"   Độ dài text: {tesseract_length} ký tự")
    if tesseract_length > 0:
        print(f"   Text preview: {tesseract_text[:100]}...")
    
    # EasyOCR results
    easyocr_text = result.get('easyocr_result', '')
    easyocr_length = len(easyocr_text)
    
    print(f"\n👁️ EASYOCR:")
    print(f"   Độ dài text: {easyocr_length} ký tự")
    if easyocr_length > 0:
        print(f"   Text preview: {easyocr_text[:100]}...")
    
    # Prescription info
    prescription_info = result.get('prescription_info', {})
    print(f"\n💊 THÔNG TIN ĐƠN THUỐC:")
    print(f"   Bệnh nhân: {prescription_info.get('patient_name', 'Không tìm thấy')}")
    print(f"   Bác sĩ: {prescription_info.get('doctor_name', 'Không tìm thấy')}")
    print(f"   Ngày: {prescription_info.get('date', 'Không tìm thấy')}")
    print(f"   Bệnh viện: {prescription_info.get('hospital', 'Không tìm thấy')}")
    
    medicines = prescription_info.get('medicines', [])
    print(f"   Thuốc ({len(medicines)} loại):")
    for i, medicine in enumerate(medicines[:5], 1):  # Chỉ hiển thị 5 thuốc đầu
        print(f"     {i}. {medicine}")
    
    dosage_instructions = prescription_info.get('dosage_instructions', [])
    print(f"   Hướng dẫn ({len(dosage_instructions)} mục):")
    for i, instruction in enumerate(dosage_instructions[:3], 1):  # Chỉ hiển thị 3 mục đầu
        print(f"     {i}. {instruction}")
    
    # Processing info
    processing_info = result.get('processing_info', {})
    print(f"\n⚙️ THÔNG TIN XỬ LÝ:")
    print(f"   Tesseract version: {processing_info.get('tesseract_version_used', 'unknown')}")
    print(f"   Tesseract config: {processing_info.get('tesseract_config_used', 'unknown')}")
    print(f"   EasyOCR version: {processing_info.get('easyocr_version_used', 'unknown')}")
    print(f"   Tổng số phiên bản test: {processing_info.get('total_versions_tested', 0)}")
    print(f"   Góc nghiêng: {processing_info.get('skew_angle', 0):.2f}°")
    print(f"   Advanced processing: {processing_info.get('advanced_processing', 'unknown')}")

def evaluate_accuracy(expected_info, extracted_info):
    """
    Đánh giá độ chính xác
    """
    score = 0
    total = 0
    
    # Kiểm tra thông tin cơ bản
    checks = [
        ('patient_name', 'Nguyễn Văn A'),
        ('doctor_name', 'BS. Trần Thị B'),
        ('date', '17/07/2025'),
    ]
    
    for field, expected in checks:
        total += 1
        extracted = extracted_info.get(field, '')
        if expected.lower() in extracted.lower():
            score += 1
            print(f"✅ {field}: ĐÚNG")
        else:
            print(f"❌ {field}: SAI (Expected: {expected}, Got: {extracted})")
    
    # Kiểm tra thuốc
    expected_medicines = ['Paracetamol', 'Amoxicillin', 'Vitamin C']
    extracted_medicines = extracted_info.get('medicines', [])
    
    for medicine in expected_medicines:
        total += 1
        found = any(medicine.lower() in med.lower() for med in extracted_medicines)
        if found:
            score += 1
            print(f"✅ Thuốc {medicine}: ĐÚNG")
        else:
            print(f"❌ Thuốc {medicine}: SAI")
    
    accuracy = (score / total) * 100 if total > 0 else 0
    print(f"\n📊 ĐỘ CHÍNH XÁC TỔNG THỂ: {accuracy:.1f}% ({score}/{total})")
    
    return accuracy

def main():
    print("🧪 TEST ĐỘ CHÍNH XÁC OCR SAU CẢI THIỆN")
    print("=" * 60)
    
    # Tạo ảnh test
    print("1. Tạo ảnh đơn thuốc test...")
    test_image = create_test_prescription_image()
    test_image.save("test_prescription.png")
    print("✅ Đã tạo test_prescription.png")
    
    # Test API
    print("\n2. Gửi request đến API...")
    start_time = time.time()
    result = test_ocr_api(test_image)
    end_time = time.time()
    
    if result:
        print(f"✅ API response trong {end_time - start_time:.2f} giây")
        
        # Phân tích kết quả
        print("\n3. Phân tích kết quả...")
        analyze_results(result)
        
        # Đánh giá độ chính xác
        print("\n4. Đánh giá độ chính xác...")
        accuracy = evaluate_accuracy({}, result.get('prescription_info', {}))
        
        # Kết luận
        print("\n" + "=" * 60)
        print("🎯 KẾT LUẬN")
        print("=" * 60)
        
        if accuracy >= 80:
            print("🎉 ĐỘ CHÍNH XÁC CAO - Hệ thống hoạt động tốt!")
        elif accuracy >= 60:
            print("⚠️ ĐỘ CHÍNH XÁC TRUNG BÌNH - Cần cải thiện thêm")
        else:
            print("❌ ĐỘ CHÍNH XÁC THẤP - Cần xem xét lại thuật toán")
        
        print(f"Thời gian xử lý: {end_time - start_time:.2f} giây")
        
    else:
        print("❌ Không thể test API")

if __name__ == "__main__":
    main()
