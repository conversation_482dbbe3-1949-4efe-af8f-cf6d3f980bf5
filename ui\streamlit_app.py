import streamlit as st
import requests
import io
from PIL import Image
import base64

# C<PERSON>u hình trang
st.set_page_config(
    page_title="Prescription OCR",
    page_icon="💊",
    layout="wide"
)

# URL của API
API_URL = "http://localhost:8000"

def main():
    st.title("💊 Trích xuất Text từ Đơn Thuốc")
    st.markdown("---")

    # Sidebar với thông tin
    with st.sidebar:
        st.header("ℹ️ Thông tin")
        st.write("Ứng dụng này giúp bạn trích xuất text từ ảnh đơn thuốc sử dụng OCR.")
        st.write("**Hỗ trợ định dạng:** JPG, PNG, JPEG")

        # Kiểm tra trạng thái API
        try:
            response = requests.get(f"{API_URL}/health", timeout=5)
            if response.status_code == 200:
                st.success("✅ API đang hoạt động")
                health_data = response.json()
                st.write(f"Tesseract: {'✅' if health_data.get('tesseract_available') else '❌'}")
                st.write(f"EasyOCR: {'✅' if health_data.get('easyocr_available') else '❌'}")
            else:
                st.error("❌ API không phản hồi")
        except:
            st.error("❌ Không thể kết nối API")

    # Main content
    col1, col2 = st.columns([1, 1])

    with col1:
        st.header("📤 Upload Ảnh")

        uploaded_file = st.file_uploader(
            "Chọn ảnh đơn thuốc",
            type=['png', 'jpg', 'jpeg'],
            help="Hỗ trợ các định dạng: PNG, JPG, JPEG"
        )

        if uploaded_file is not None:
            # Hiển thị ảnh đã upload
            image = Image.open(uploaded_file)
            st.image(image, caption="Ảnh đã upload", use_container_width=True)

            # Nút xử lý
            if st.button("🔍 Trích xuất Text", type="primary"):
                with st.spinner("Đang xử lý ảnh..."):
                    try:
                        # Gửi ảnh đến API
                        files = {"file": uploaded_file.getvalue()}
                        response = requests.post(
                            f"{API_URL}/extract-text",
                            files={"file": (uploaded_file.name, uploaded_file.getvalue(), uploaded_file.type)}
                        )

                        if response.status_code == 200:
                            result = response.json()

                            # Hiển thị kết quả trong cột 2
                            with col2:
                                st.header("📄 Kết quả OCR")

                                # Tab cho các kết quả khác nhau
                                tab1, tab2, tab3 = st.tabs(["Thông tin đơn thuốc", "Tesseract OCR", "EasyOCR"])

                                with tab1:
                                    st.subheader("💊 Thông tin trích xuất từ đơn thuốc")
                                    prescription_info = result.get('prescription_info', {})

                                    # Hiển thị thông tin bệnh nhân
                                    if prescription_info.get('patient_name'):
                                        st.write(f"**👤 Bệnh nhân:** {prescription_info['patient_name']}")

                                    # Hiển thị thông tin bác sĩ
                                    if prescription_info.get('doctor_name'):
                                        st.write(f"**👨‍⚕️ Bác sĩ:** {prescription_info['doctor_name']}")

                                    # Hiển thị ngày
                                    if prescription_info.get('date'):
                                        st.write(f"**📅 Ngày:** {prescription_info['date']}")

                                    # Hiển thị bệnh viện
                                    if prescription_info.get('hospital'):
                                        st.write(f"**🏥 Bệnh viện:** {prescription_info['hospital']}")

                                    # Hiển thị danh sách thuốc
                                    medicines = prescription_info.get('medicines', [])
                                    if medicines:
                                        st.write("**💊 Thuốc được kê:**")
                                        for i, medicine in enumerate(medicines, 1):
                                            st.write(f"{i}. {medicine}")

                                    # Hiển thị hướng dẫn sử dụng
                                    dosage_instructions = prescription_info.get('dosage_instructions', [])
                                    if dosage_instructions:
                                        st.write("**📋 Hướng dẫn sử dụng:**")
                                        for instruction in dosage_instructions:
                                            st.write(f"• {instruction}")

                                    if not any([prescription_info.get('patient_name'),
                                              prescription_info.get('doctor_name'),
                                              prescription_info.get('date'),
                                              medicines]):
                                        st.info("Không tìm thấy thông tin đơn thuốc cụ thể. Hãy xem tab OCR để xem text đã trích xuất.")

                                with tab2:
                                    st.subheader("Kết quả Tesseract")
                                    tesseract_text = result.get('tesseract_result', '')
                                    if tesseract_text:
                                        st.text_area(
                                            "Text được trích xuất:",
                                            tesseract_text,
                                            height=300,
                                            key="tesseract"
                                        )

                                        # Hiển thị chi tiết nếu có
                                        detailed_tesseract = result.get('detailed_results', {}).get('tesseract', {})
                                        if detailed_tesseract.get('words'):
                                            st.subheader("Chi tiết từng từ")
                                            words_df = []
                                            for word in detailed_tesseract['words']:
                                                if word['text'].strip():
                                                    words_df.append({
                                                        'Từ': word['text'],
                                                        'Độ tin cậy': f"{word['confidence']:.1f}%"
                                                    })
                                            if words_df:
                                                st.dataframe(words_df, use_container_width=True)
                                    else:
                                        st.warning("Không tìm thấy text nào")

                                with tab3:
                                    st.subheader("Kết quả EasyOCR")
                                    easyocr_text = result.get('easyocr_result', '')
                                    if easyocr_text:
                                        st.text_area(
                                            "Text được trích xuất:",
                                            easyocr_text,
                                            height=300,
                                            key="easyocr"
                                        )

                                        # Hiển thị chi tiết nếu có
                                        detailed_easyocr = result.get('detailed_results', {}).get('easyocr', {})
                                        if detailed_easyocr.get('words'):
                                            st.subheader("Chi tiết từng từ")
                                            words_df = []
                                            for word in detailed_easyocr['words']:
                                                if word['text'].strip():
                                                    words_df.append({
                                                        'Từ': word['text'],
                                                        'Độ tin cậy': f"{word['confidence']:.1f}%"
                                                    })
                                            if words_df:
                                                st.dataframe(words_df, use_container_width=True)
                                    else:
                                        st.warning("Không tìm thấy text nào")

                                # So sánh kết quả
                                if tesseract_text and easyocr_text:
                                    st.subheader("📊 So sánh")
                                    col_a, col_b = st.columns(2)
                                    with col_a:
                                        st.metric("Tesseract", f"{len(tesseract_text)} ký tự")
                                    with col_b:
                                        st.metric("EasyOCR", f"{len(easyocr_text)} ký tự")

                        else:
                            st.error(f"Lỗi API: {response.status_code}")
                            st.write(response.text)

                    except requests.exceptions.ConnectionError:
                        st.error("❌ Không thể kết nối đến API. Hãy đảm bảo server đang chạy.")
                    except Exception as e:
                        st.error(f"❌ Lỗi: {str(e)}")

    with col2:
        if 'uploaded_file' not in locals() or uploaded_file is None:
            st.header("📄 Kết quả OCR")
            st.info("👆 Hãy upload ảnh để xem kết quả OCR")

    # Footer
    st.markdown("---")
    st.markdown(
        """
        <div style='text-align: center'>
            <p>💊 Prescription OCR - Trích xuất text từ đơn thuốc</p>
        </div>
        """,
        unsafe_allow_html=True
    )

if __name__ == "__main__":
    main()
