import cv2
import numpy as np
from PIL import Image, ImageEnhance
from typing import Tuple, List
import logging

logger = logging.getLogger(__name__)

class PrescriptionImageProcessor:
    """
    Class chuyên xử lý ảnh đơn thuốc để tối ưu cho OCR
    """

    @staticmethod
    def enhance_contrast(image: np.ndarray, factor: float = 1.5) -> np.ndarray:
        """
        Tăng độ tương phản của ảnh
        """
        # Chuyển đổi sang PIL Image
        pil_image = Image.fromarray(image)
        enhancer = ImageEnhance.Contrast(pil_image)
        enhanced = enhancer.enhance(factor)
        return np.array(enhanced)

    @staticmethod
    def enhance_sharpness(image: np.ndarray, factor: float = 2.0) -> np.ndarray:
        """
        Tăng độ sắc nét của ảnh
        """
        pil_image = Image.fromarray(image)
        enhancer = ImageEnhance.Sharpness(pil_image)
        enhanced = enhancer.enhance(factor)
        return np.array(enhanced)

    @staticmethod
    def remove_noise(image: np.ndarray) -> np.ndarray:
        """
        Loại bỏ noise từ ảnh
        """
        # Sử dụng Non-local Means Denoising
        if len(image.shape) == 3:
            denoised = cv2.fastNlMeansDenoisingColored(image, None, 10, 10, 7, 21)
        else:
            denoised = cv2.fastNlMeansDenoising(image, None, 10, 7, 21)
        return denoised

    @staticmethod
    def correct_skew(image: np.ndarray) -> np.ndarray:
        """
        Sửa độ nghiêng của ảnh
        """
        # Chuyển sang grayscale nếu cần
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # Tìm các cạnh
        edges = cv2.Canny(gray, 50, 150, apertureSize=3)

        # Tìm các đường thẳng
        lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)

        if lines is not None:
            # Tính góc nghiêng trung bình
            angles = []
            for rho, theta in lines[:10]:  # Chỉ lấy 10 đường đầu tiên
                angle = theta * 180 / np.pi
                if angle < 45:
                    angles.append(angle)
                elif angle > 135:
                    angles.append(angle - 180)

            if angles:
                median_angle = np.median(angles)

                # Xoay ảnh
                (h, w) = image.shape[:2]
                center = (w // 2, h // 2)
                M = cv2.getRotationMatrix2D(center, median_angle, 1.0)
                rotated = cv2.warpAffine(image, M, (w, h), flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE)
                return rotated

        return image

    @staticmethod
    def adaptive_threshold(image: np.ndarray) -> np.ndarray:
        """
        Áp dụng adaptive threshold tối ưu cho đơn thuốc
        """
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # Thử nhiều phương pháp threshold và chọn tốt nhất
        methods = []

        # Adaptive threshold methods
        try:
            methods.append(cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2))
        except:
            pass

        try:
            methods.append(cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_MEAN_C, cv2.THRESH_BINARY, 11, 2))
        except:
            pass

        # OTSU threshold
        try:
            _, otsu_thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            methods.append(otsu_thresh)
        except:
            pass

        # Fallback: simple threshold
        if not methods:
            _, simple_thresh = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
            methods.append(simple_thresh)

        # Chọn phương pháp có nhiều pixel trắng nhất (thường tốt cho text)
        best_method = max(methods, key=lambda x: np.sum(x == 255))
        return best_method

    @staticmethod
    def morphological_operations(image: np.ndarray) -> np.ndarray:
        """
        Áp dụng các phép toán morphological để làm sạch ảnh
        """
        # Kernel cho các phép toán
        kernel_close = np.ones((2, 2), np.uint8)
        kernel_open = np.ones((1, 1), np.uint8)

        # Closing để nối các phần bị đứt
        closed = cv2.morphologyEx(image, cv2.MORPH_CLOSE, kernel_close)

        # Opening để loại bỏ noise nhỏ
        opened = cv2.morphologyEx(closed, cv2.MORPH_OPEN, kernel_open)

        return opened

    @staticmethod
    def resize_for_ocr(image: np.ndarray, target_height: int = 1000) -> np.ndarray:
        """
        Resize ảnh để tối ưu cho OCR
        """
        height, width = image.shape[:2]

        # Chỉ resize nếu ảnh quá nhỏ
        if height < target_height:
            scale = target_height / height
            new_width = int(width * scale)
            resized = cv2.resize(image, (new_width, target_height), interpolation=cv2.INTER_CUBIC)
            return resized

        return image

    def process_prescription_image(self, image: np.ndarray, aggressive: bool = False) -> np.ndarray:
        """
        Xử lý hoàn chỉnh ảnh đơn thuốc

        Args:
            image: Ảnh đầu vào
            aggressive: Nếu True, áp dụng xử lý mạnh hơn
        """
        try:
            # Bước 1: Resize nếu cần
            processed = self.resize_for_ocr(image)

            # Bước 2: Loại bỏ noise
            if aggressive:
                processed = self.remove_noise(processed)

            # Bước 3: Tăng contrast
            processed = self.enhance_contrast(processed, 1.3 if not aggressive else 1.8)

            # Bước 4: Tăng độ sắc nét
            processed = self.enhance_sharpness(processed, 1.5 if not aggressive else 2.5)

            # Bước 5: Sửa độ nghiêng
            if aggressive:
                processed = self.correct_skew(processed)

            # Bước 6: Chuyển sang grayscale
            if len(processed.shape) == 3:
                processed = cv2.cvtColor(processed, cv2.COLOR_BGR2GRAY)

            # Bước 7: Adaptive threshold
            processed = self.adaptive_threshold(processed)

            # Bước 8: Morphological operations
            processed = self.morphological_operations(processed)

            return processed

        except Exception as e:
            logger.error(f"Error in image processing: {e}")
            # Trả về ảnh gốc nếu có lỗi
            if len(image.shape) == 3:
                return cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            return image

    def create_multiple_versions(self, image: np.ndarray) -> List[Tuple[str, np.ndarray]]:
        """
        Tạo nhiều phiên bản xử lý khác nhau của ảnh
        """
        versions = []

        # Phiên bản gốc
        if len(image.shape) == 3:
            gray_original = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray_original = image.copy()
        versions.append(("original", gray_original))

        # Phiên bản xử lý nhẹ
        light_processed = self.process_prescription_image(image, aggressive=False)
        versions.append(("light_processing", light_processed))

        # Phiên bản xử lý mạnh
        aggressive_processed = self.process_prescription_image(image, aggressive=True)
        versions.append(("aggressive_processing", aggressive_processed))

        # Phiên bản chỉ tăng contrast
        contrast_only = self.enhance_contrast(gray_original, 2.0)
        contrast_only = self.adaptive_threshold(contrast_only)
        versions.append(("contrast_only", contrast_only))

        return versions
