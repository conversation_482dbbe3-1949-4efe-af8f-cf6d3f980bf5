#!/usr/bin/env python3
"""
Script test riêng cho Tesseract
"""
import subprocess
import sys
import os

def test_command_line():
    """Test tesseract từ command line"""
    print("🔍 Test Tesseract từ command line...")
    try:
        result = subprocess.run(['tesseract', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ Command line tesseract hoạt động")
            print(f"Output: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ Command line tesseract lỗi: {result.stderr}")
            return False
    except FileNotFoundError:
        print("❌ Không tìm thấy tesseract command")
        return False
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False

def test_pytesseract():
    """Test pytesseract"""
    print("\n🔍 Test pytesseract...")
    try:
        import pytesseract
        print("✅ Import pytesseract thành công")
        
        # <PERSON><PERSON><PERSON> tra đường dẫn mặc định
        print(f"Pytesseract cmd: {pytesseract.pytesseract.tesseract_cmd}")
        
        # Test get version
        version = pytesseract.get_tesseract_version()
        print(f"✅ Pytesseract version: {version}")
        return True
        
    except Exception as e:
        print(f"❌ Pytesseract lỗi: {e}")
        
        # Thử set đường dẫn thủ công
        print("\n🔧 Thử set đường dẫn thủ công...")
        try:
            import pytesseract
            
            # Các đường dẫn có thể có
            possible_paths = [
                r"C:\Program Files\Tesseract-OCR\tesseract.exe",
                r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
                r"C:\Tesseract-OCR\tesseract.exe",
            ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    print(f"✅ Tìm thấy tesseract tại: {path}")
                    pytesseract.pytesseract.tesseract_cmd = path
                    
                    try:
                        version = pytesseract.get_tesseract_version()
                        print(f"✅ Pytesseract hoạt động với đường dẫn: {path}")
                        print(f"Version: {version}")
                        return True
                    except Exception as e2:
                        print(f"❌ Vẫn lỗi với đường dẫn {path}: {e2}")
                        continue
            
            print("❌ Không tìm thấy tesseract.exe ở các vị trí thông thường")
            return False
            
        except Exception as e2:
            print(f"❌ Lỗi khi set đường dẫn: {e2}")
            return False

def test_simple_ocr():
    """Test OCR đơn giản"""
    print("\n🧪 Test OCR đơn giản...")
    try:
        import pytesseract
        from PIL import Image
        import numpy as np
        
        # Tạo ảnh test đơn giản
        img_array = np.ones((100, 300, 3), dtype=np.uint8) * 255  # Ảnh trắng
        img = Image.fromarray(img_array)
        
        # Thêm text vào ảnh (giả lập)
        from PIL import ImageDraw
        draw = ImageDraw.Draw(img)
        draw.text((10, 30), "Hello World", fill=(0, 0, 0))
        
        # Test OCR
        text = pytesseract.image_to_string(img)
        print(f"✅ OCR test thành công: '{text.strip()}'")
        return True
        
    except Exception as e:
        print(f"❌ OCR test lỗi: {e}")
        return False

def main():
    print("🔍 KIỂM TRA CHI TIẾT TESSERACT")
    print("=" * 50)
    
    # Test command line
    cmd_ok = test_command_line()
    
    # Test pytesseract
    py_ok = test_pytesseract()
    
    # Test OCR nếu pytesseract OK
    if py_ok:
        ocr_ok = test_simple_ocr()
    else:
        ocr_ok = False
    
    print("\n" + "=" * 50)
    print("📊 KẾT QUẢ")
    print("=" * 50)
    print(f"Command line tesseract: {'✅' if cmd_ok else '❌'}")
    print(f"Pytesseract: {'✅' if py_ok else '❌'}")
    print(f"OCR test: {'✅' if ocr_ok else '❌'}")
    
    if not py_ok:
        print("\n💡 GIẢI PHÁP:")
        print("1. Kiểm tra Tesseract đã được cài đặt")
        print("2. Thêm Tesseract vào PATH")
        print("3. Hoặc set đường dẫn trong code:")
        print("   import pytesseract")
        print("   pytesseract.pytesseract.tesseract_cmd = r'C:\\Program Files\\Tesseract-OCR\\tesseract.exe'")

if __name__ == "__main__":
    main()
