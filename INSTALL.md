# 📋 Hướng dẫn Cài đặt Chi tiết

## 🔧 Cài đặt Tesseract OCR

### Windows

#### Cách 1: T<PERSON><PERSON> trực tiếp
1. <PERSON><PERSON><PERSON> cập: https://github.com/UB-Mannheim/tesseract/wiki
2. Tải file installer phù hợp với hệ thống (32-bit hoặc 64-bit)
3. Chạy installer và làm theo hướng dẫn
4. Thêm đường dẫn Tesseract vào PATH:
   - Mở System Properties → Advanced → Environment Variables
   - Thêm `C:\Program Files\Tesseract-OCR` vào PATH
   - Restart Command Prompt

#### Cách 2: Sử dụng Chocolatey
```powershell
# Cài đặt Chocolatey nếu chưa có
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# Cài đặt Tesseract
choco install tesseract
```

#### Cách 3: Sử dụng Scoop
```powershell
# Cài đặt Scoop nếu chưa có
Set-ExecutionPolicy RemoteSigned -Scope CurrentUser
irm get.scoop.sh | iex

# Cài đặt Tesseract
scoop install tesseract
```

### Ubuntu/Debian

```bash
# Cập nhật package list
sudo apt update

# Cài đặt Tesseract và language packs
sudo apt install tesseract-ocr tesseract-ocr-vie tesseract-ocr-eng

# Kiểm tra cài đặt
tesseract --version
tesseract --list-langs
```

### CentOS/RHEL/Fedora

```bash
# CentOS/RHEL
sudo yum install epel-release
sudo yum install tesseract tesseract-langpack-vie tesseract-langpack-eng

# Fedora
sudo dnf install tesseract tesseract-langpack-vie tesseract-langpack-eng
```

### macOS

#### Cách 1: Homebrew
```bash
# Cài đặt Homebrew nếu chưa có
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Cài đặt Tesseract
brew install tesseract tesseract-lang
```

#### Cách 2: MacPorts
```bash
sudo port install tesseract-vie tesseract-eng
```

## 🐍 Cài đặt Python Environment

### Sử dụng venv (Khuyến nghị)

```bash
# Tạo virtual environment
python -m venv prescription_ocr_env

# Kích hoạt environment
# Windows:
prescription_ocr_env\Scripts\activate
# Linux/macOS:
source prescription_ocr_env/bin/activate

# Cài đặt dependencies
pip install -r requirements.txt
```

### Sử dụng conda

```bash
# Tạo conda environment
conda create -n prescription_ocr python=3.9

# Kích hoạt environment
conda activate prescription_ocr

# Cài đặt dependencies
pip install -r requirements.txt
```

## 📦 Cài đặt Dependencies

### Cài đặt từ requirements.txt

```bash
pip install -r requirements.txt
```

### Cài đặt thủ công từng package

```bash
# Core dependencies
pip install fastapi==0.104.1
pip install uvicorn==0.24.0
pip install streamlit==1.28.1

# Image processing
pip install pillow==10.1.0
pip install opencv-python==********

# OCR libraries
pip install pytesseract==0.3.10
pip install easyocr==1.7.0

# Utilities
pip install python-multipart==0.0.6
pip install numpy==1.24.3
pip install requests==2.31.0
pip install python-dotenv==1.0.0
```

## 🔍 Kiểm tra Cài đặt

### Kiểm tra Tesseract

```bash
# Kiểm tra version
tesseract --version

# Kiểm tra languages
tesseract --list-langs

# Test OCR
echo "Hello World" | tesseract stdin stdout
```

### Kiểm tra Python packages

```python
# Tạo file test_installation.py
import cv2
import numpy as np
from PIL import Image
import pytesseract
import easyocr
import fastapi
import streamlit

print("✅ Tất cả packages đã được cài đặt thành công!")

# Test Tesseract
try:
    print(f"Tesseract version: {pytesseract.get_tesseract_version()}")
    print("✅ Tesseract hoạt động bình thường")
except:
    print("❌ Lỗi Tesseract")

# Test EasyOCR
try:
    reader = easyocr.Reader(['en'])
    print("✅ EasyOCR hoạt động bình thường")
except:
    print("❌ Lỗi EasyOCR")
```

Chạy test:
```bash
python test_installation.py
```

## 🚨 Xử lý Lỗi Thường Gặp

### Lỗi "tesseract is not installed"

**Windows:**
```bash
# Thêm Tesseract vào PATH
set PATH=%PATH%;C:\Program Files\Tesseract-OCR

# Hoặc set biến môi trường TESSDATA_PREFIX
set TESSDATA_PREFIX=C:\Program Files\Tesseract-OCR\tessdata
```

**Linux/macOS:**
```bash
# Kiểm tra đường dẫn Tesseract
which tesseract

# Nếu không tìm thấy, cài đặt lại
sudo apt install tesseract-ocr  # Ubuntu
brew install tesseract          # macOS
```

### Lỗi EasyOCR "No module named 'torch'"

```bash
# Cài đặt PyTorch trước
pip install torch torchvision

# Sau đó cài đặt EasyOCR
pip install easyocr
```

### Lỗi OpenCV

```bash
# Gỡ cài đặt và cài lại
pip uninstall opencv-python
pip install opencv-python-headless
```

### Lỗi Permission (Linux/macOS)

```bash
# Sử dụng sudo nếu cần
sudo pip install -r requirements.txt

# Hoặc cài đặt cho user hiện tại
pip install --user -r requirements.txt
```

## 🔧 Cấu hình Nâng cao

### Cấu hình Tesseract cho tiếng Việt

1. Tải language data cho tiếng Việt:
```bash
# Ubuntu
sudo apt install tesseract-ocr-vie

# Windows: Tải từ https://github.com/tesseract-ocr/tessdata
# Copy file vie.traineddata vào thư mục tessdata
```

2. Test tiếng Việt:
```bash
tesseract image.jpg output -l vie
```

### Cấu hình GPU cho EasyOCR (Tùy chọn)

```bash
# Cài đặt CUDA version của PyTorch
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# Cài đặt EasyOCR
pip install easyocr
```

## ✅ Hoàn tất Cài đặt

Sau khi hoàn tất tất cả các bước trên:

1. Kiểm tra lại tất cả dependencies
2. Chạy test script
3. Khởi động ứng dụng:

```bash
# Terminal 1: Chạy API
python run_api.py

# Terminal 2: Chạy UI
python run_ui.py
```

4. Truy cập http://localhost:8501 để sử dụng ứng dụng
