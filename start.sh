#!/bin/bash

echo "========================================"
echo "    PRESCRIPTION OCR - KHỞI ĐỘNG"
echo "========================================"

# Kích hoạt virtual environment nếu có
if [ -f "prescription_ocr_env/bin/activate" ]; then
    echo "Kích hoạt virtual environment..."
    source prescription_ocr_env/bin/activate
fi

# Kiểm tra cài đặt
echo "Kiểm tra cài đặt..."
python test_installation.py
if [ $? -ne 0 ]; then
    echo ""
    echo "CÀI ĐẶT CHƯA HOÀN TẤT! Vui lòng xem INSTALL.md"
    read -p "Nhấn Enter để thoát..."
    exit 1
fi

echo ""
echo "========================================"
echo "Chọn chế độ chạy:"
echo "1. Chỉ chạy API Server (port 8000)"
echo "2. Chỉ chạy Streamlit UI (port 8501)"
echo "3. <PERSON><PERSON><PERSON> cả hai (khuyến nghị)"
echo "========================================"
read -p "Nhập lựa chọn (1-3): " choice

case $choice in
    1)
        echo "Khởi động API Server..."
        python run_api.py
        ;;
    2)
        echo "Khởi động Streamlit UI..."
        python run_ui.py
        ;;
    3)
        echo "Khởi động cả API và UI..."
        
        # Chạy API server trong background
        python run_api.py &
        API_PID=$!
        
        # Đợi API khởi động
        echo "Đợi API server khởi động..."
        sleep 3
        
        # Chạy Streamlit UI
        python run_ui.py &
        UI_PID=$!
        
        echo ""
        echo "Đã khởi động cả hai service!"
        echo "API: http://localhost:8000"
        echo "UI:  http://localhost:8501"
        echo ""
        echo "Nhấn Ctrl+C để dừng tất cả services"
        
        # Trap để dọn dẹp khi thoát
        trap "echo 'Đang dừng services...'; kill $API_PID $UI_PID 2>/dev/null; exit" INT TERM
        
        # Đợi
        wait
        ;;
    *)
        echo "Lựa chọn không hợp lệ!"
        exit 1
        ;;
esac
