import cv2
import numpy as np
from PIL import Image
import pytesseract
import easyocr
import re
from typing import Dict, List
import logging

logger = logging.getLogger(__name__)

class SimpleOCRProcessor:
    """
    OCR processor đơn giản nhưng hiệu quả
    """
    
    def __init__(self):
        try:
            self.easyocr_reader = easyocr.Reader(['en'])  # Chỉ dùng tiếng Anh để tránh lỗi
            logger.info("EasyOCR reader initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize EasyOCR: {e}")
            self.easyocr_reader = None
    
    def preprocess_image_simple(self, image: np.ndarray) -> np.ndarray:
        """
        Tiền xử lý ảnh đơn giản nhưng hiệu quả
        """
        try:
            # <PERSON><PERSON><PERSON><PERSON> sang grayscale
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # Resize nếu ảnh quá nhỏ
            height, width = gray.shape
            if height < 600:
                scale = 600 / height
                new_width = int(width * scale)
                gray = cv2.resize(gray, (new_width, 600), interpolation=cv2.INTER_CUBIC)
            
            # Tăng contrast
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)
            
            # Giảm noise
            denoised = cv2.medianBlur(enhanced, 3)
            
            # Adaptive threshold
            binary = cv2.adaptiveThreshold(
                denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY, 11, 2
            )
            
            # Morphological operations
            kernel = np.ones((2, 2), np.uint8)
            cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            
            return cleaned
            
        except Exception as e:
            logger.error(f"Error in preprocess_image_simple: {e}")
            return image if len(image.shape) == 2 else cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    def extract_text_tesseract_simple(self, image: np.ndarray) -> Dict:
        """
        Tesseract OCR đơn giản
        """
        try:
            # Chỉ dùng config đơn giản
            config = '--oem 3 --psm 6 -l eng'
            text = pytesseract.image_to_string(image, config=config)
            
            return {
                'text': text.strip(),
                'method': 'tesseract_simple',
                'config': config
            }
            
        except Exception as e:
            logger.error(f"Tesseract simple error: {e}")
            return {'text': '', 'method': 'tesseract_simple', 'error': str(e)}
    
    def extract_text_easyocr_simple(self, image: np.ndarray) -> Dict:
        """
        EasyOCR đơn giản
        """
        try:
            if self.easyocr_reader is None:
                return {'text': '', 'method': 'easyocr_simple', 'error': 'EasyOCR not available'}
            
            results = self.easyocr_reader.readtext(image)
            text_lines = []
            
            for (bbox, text, confidence) in results:
                if confidence > 0.3:  # Confidence > 30%
                    text_lines.append(text)
            
            return {
                'text': '\n'.join(text_lines),
                'method': 'easyocr_simple'
            }
            
        except Exception as e:
            logger.error(f"EasyOCR simple error: {e}")
            return {'text': '', 'method': 'easyocr_simple', 'error': str(e)}
    
    def extract_prescription_info_simple(self, text: str) -> Dict:
        """
        Trích xuất thông tin đơn thuốc đơn giản
        """
        info = {
            'medicines': [],
            'doctor_name': '',
            'patient_name': '',
            'date': '',
            'hospital': ''
        }
        
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        
        for line in lines:
            line_lower = line.lower()
            
            # Tìm bệnh nhân
            if not info['patient_name']:
                if any(keyword in line_lower for keyword in ['patient', 'benh nhan', 'name']):
                    # Tìm tên sau dấu :
                    if ':' in line:
                        name = line.split(':', 1)[1].strip()
                        if len(name) > 2:
                            info['patient_name'] = name
            
            # Tìm bác sĩ
            if not info['doctor_name']:
                if any(keyword in line_lower for keyword in ['doctor', 'bac si', 'dr', 'bs']):
                    if ':' in line:
                        doctor = line.split(':', 1)[1].strip()
                        if len(doctor) > 2:
                            info['doctor_name'] = doctor
                    else:
                        # Tìm pattern "BS. Tên" hoặc "Dr. Tên"
                        match = re.search(r'(?:BS\.?|Dr\.?)\s+([A-Za-z\s]+)', line)
                        if match:
                            info['doctor_name'] = match.group(1).strip()
            
            # Tìm ngày
            if not info['date']:
                date_match = re.search(r'(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})', line)
                if date_match:
                    info['date'] = date_match.group(1)
            
            # Tìm thuốc (tìm các từ có chứa mg, ml, viên)
            medicine_patterns = [
                r'([A-Za-z]+(?:\s+[A-Za-z]+)*)\s*\d+\s*(?:mg|ml|g)',
                r'([A-Za-z]+(?:\s+[A-Za-z]+)*)\s*-\s*\d+\s*viên'
            ]
            
            for pattern in medicine_patterns:
                matches = re.findall(pattern, line)
                for match in matches:
                    medicine = match.strip()
                    if len(medicine) > 2 and medicine not in info['medicines']:
                        info['medicines'].append(medicine)
        
        return info
    
    def process_prescription_simple(self, image: np.ndarray) -> Dict:
        """
        Xử lý đơn thuốc đơn giản
        """
        # Tiền xử lý ảnh
        processed_image = self.preprocess_image_simple(image)
        
        # OCR với cả hai phương pháp
        tesseract_result = self.extract_text_tesseract_simple(processed_image)
        easyocr_result = self.extract_text_easyocr_simple(image)  # EasyOCR dùng ảnh gốc
        
        # Kết hợp text
        combined_text = tesseract_result['text'] + '\n' + easyocr_result['text']
        
        # Trích xuất thông tin
        prescription_info = self.extract_prescription_info_simple(combined_text)
        
        return {
            'tesseract_result': tesseract_result['text'],
            'easyocr_result': easyocr_result['text'],
            'prescription_info': prescription_info,
            'combined_text': combined_text,
            'processing_info': {
                'method': 'simple_processing',
                'tesseract_config': tesseract_result.get('config', 'unknown')
            }
        }
