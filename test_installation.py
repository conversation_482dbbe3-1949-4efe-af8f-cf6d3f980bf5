#!/usr/bin/env python3
"""
Script kiểm tra cài đặt cho Prescription OCR
"""

import sys
import subprocess
import importlib

def check_python_version():
    """Kiểm tra phiên bản Python"""
    print("🐍 Kiểm tra Python version...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - OK")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - Cần Python 3.8+")
        return False

def check_package(package_name, import_name=None):
    """Kiểm tra package có được cài đặt không"""
    if import_name is None:
        import_name = package_name

    try:
        module = importlib.import_module(import_name)
        version = getattr(module, '__version__', 'Unknown')
        print(f"✅ {package_name} ({version}) - OK")
        return True
    except ImportError:
        print(f"❌ {package_name} - Chưa cài đặt")
        return False

def check_tesseract():
    """Kiểm tra Tesseract OCR"""
    print("\n🔍 Kiểm tra Tesseract OCR...")
    try:
        import pytesseract
        version = pytesseract.get_tesseract_version()
        print(f"✅ Tesseract {version} - OK")

        # Kiểm tra languages
        try:
            result = subprocess.run(['tesseract', '--list-langs'],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                langs = result.stdout.strip().split('\n')[1:]  # Bỏ dòng đầu
                print(f"📝 Ngôn ngữ hỗ trợ: {', '.join(langs[:5])}...")

                if 'vie' in langs:
                    print("✅ Hỗ trợ tiếng Việt - OK")
                else:
                    print("⚠️ Chưa hỗ trợ tiếng Việt")

                if 'eng' in langs:
                    print("✅ Hỗ trợ tiếng Anh - OK")
                else:
                    print("⚠️ Chưa hỗ trợ tiếng Anh")
            else:
                print("⚠️ Không thể kiểm tra ngôn ngữ")
        except:
            print("⚠️ Không thể kiểm tra ngôn ngữ")

        return True
    except Exception as e:
        print(f"❌ Tesseract - Lỗi: {e}")
        return False

def check_easyocr():
    """Kiểm tra EasyOCR"""
    print("\n👁️ Kiểm tra EasyOCR...")
    try:
        import easyocr
        print(f"✅ EasyOCR {easyocr.__version__} - OK")

        # Test khởi tạo reader
        try:
            reader = easyocr.Reader(['en'], verbose=False)
            print("✅ EasyOCR Reader khởi tạo thành công")
            return True
        except Exception as e:
            print(f"⚠️ EasyOCR Reader - Lỗi: {e}")
            return False
    except Exception as e:
        print(f"❌ EasyOCR - Lỗi: {e}")
        return False

def check_opencv():
    """Kiểm tra OpenCV"""
    print("\n📷 Kiểm tra OpenCV...")
    try:
        import cv2
        print(f"✅ OpenCV {cv2.__version__} - OK")

        # Test tạo ảnh đơn giản
        import numpy as np
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        gray = cv2.cvtColor(test_image, cv2.COLOR_BGR2GRAY)
        print("✅ OpenCV xử lý ảnh - OK")
        return True
    except Exception as e:
        print(f"❌ OpenCV - Lỗi: {e}")
        return False

def check_web_frameworks():
    """Kiểm tra FastAPI và Streamlit"""
    print("\n🌐 Kiểm tra Web Frameworks...")

    fastapi_ok = check_package("FastAPI", "fastapi")
    uvicorn_ok = check_package("Uvicorn", "uvicorn")
    streamlit_ok = check_package("Streamlit", "streamlit")

    return fastapi_ok and uvicorn_ok and streamlit_ok

def test_basic_ocr():
    """Test OCR cơ bản"""
    print("\n🧪 Test OCR cơ bản...")
    try:
        import numpy as np
        from PIL import Image, ImageDraw, ImageFont
        import pytesseract

        # Tạo ảnh test đơn giản
        img = Image.new('RGB', (300, 100), color='white')
        draw = ImageDraw.Draw(img)

        # Vẽ text
        try:
            # Thử sử dụng font mặc định
            draw.text((10, 30), "Hello World", fill='black')
        except:
            # Fallback nếu không có font
            draw.text((10, 30), "Hello World", fill='black')

        # Convert sang numpy array
        img_array = np.array(img)

        # Test Tesseract
        text = pytesseract.image_to_string(img_array)
        if "Hello" in text or "World" in text:
            print("✅ Tesseract OCR test - OK")
        else:
            print(f"⚠️ Tesseract OCR test - Kết quả: '{text.strip()}'")

        return True
    except Exception as e:
        print(f"❌ OCR test - Lỗi: {e}")
        return False

def main():
    """Hàm chính"""
    print("🔍 KIỂM TRA CÀI ĐẶT PRESCRIPTION OCR")
    print("=" * 50)

    checks = []

    # Kiểm tra Python
    checks.append(check_python_version())

    # Kiểm tra packages cơ bản
    print("\n📦 Kiểm tra Python packages...")
    packages = [
        ("NumPy", "numpy"),
        ("Pillow", "PIL"),
        ("Requests", "requests"),
        ("Python-dotenv", "dotenv")
    ]

    for name, import_name in packages:
        checks.append(check_package(name, import_name))

    # Kiểm tra OCR
    checks.append(check_tesseract())
    checks.append(check_easyocr())

    # Kiểm tra OpenCV
    checks.append(check_opencv())

    # Kiểm tra web frameworks
    checks.append(check_web_frameworks())

    # Test OCR
    checks.append(test_basic_ocr())

    # Tổng kết
    print("\n" + "=" * 50)
    print("📊 KẾT QUẢ KIỂM TRA")
    print("=" * 50)

    passed = sum(checks)
    total = len(checks)

    if passed == total:
        print("🎉 TẤT CẢ KIỂM TRA THÀNH CÔNG!")
        print("✅ Bạn có thể chạy ứng dụng:")
        print("   python run_api.py")
        print("   python run_ui.py")
    else:
        print(f"⚠️ {passed}/{total} kiểm tra thành công")
        print("❌ Vui lòng xem lại các lỗi ở trên và cài đặt lại")
        print("📖 Xem INSTALL.md để biết hướng dẫn chi tiết")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
