import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import pytesseract
import easyocr
import re
from typing import List, Tuple, Dict
import logging
import torch
from .vietnamese_medicine_patterns import VietnameseMedicineExtractor

logger = logging.getLogger(__name__)

class GPUOptimizedOCRProcessor:
    """
    OCR processor tối ưu hóa cho GPU với độ chính xác cao
    """

    def __init__(self, use_gpu=True):
        self.use_gpu = use_gpu and torch.cuda.is_available()

        try:
            # Khởi tạo EasyOCR với GPU nếu có
            if self.use_gpu:
                self.easyocr_reader = easyocr.Reader(['vi', 'en'], gpu=True)
                logger.info("EasyOCR initialized with GPU support")
            else:
                self.easyocr_reader = easyocr.Reader(['vi', 'en'], gpu=False)
                logger.info("EasyOCR initialized with CPU only")
        except Exception as e:
            logger.error(f"Failed to initialize EasyOCR: {e}")
            self.easyocr_reader = None

        # Khởi tạo Vietnamese medicine extractor
        self.medicine_extractor = VietnameseMedicineExtractor()

        # <PERSON><PERSON>u hình Tesseract tối ưu
        self.tesseract_configs = [
            '--oem 3 --psm 6 -l eng',      # Mixed text, uniform block
            '--oem 3 --psm 4 -l eng',      # Single column of text
            '--oem 3 --psm 3 -l eng',      # Fully automatic page segmentation
            '--oem 1 --psm 6 -l eng',      # Neural nets LSTM engine
            '--oem 3 --psm 8 -l eng',      # Single word
            '--oem 3 --psm 7 -l eng',      # Single text line
        ]

    def ultra_enhance_image(self, image: np.ndarray) -> List[np.ndarray]:
        """
        Tạo nhiều phiên bản ảnh được tối ưu hóa cực mạnh
        """
        enhanced_versions = []

        # Chuyển sang PIL để xử lý
        if len(image.shape) == 3:
            pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        else:
            pil_image = Image.fromarray(image)

        # Version 1: Super Sharp + High Contrast
        version1 = pil_image.copy()
        version1 = ImageEnhance.Sharpness(version1).enhance(3.0)
        version1 = ImageEnhance.Contrast(version1).enhance(2.5)
        version1 = version1.filter(ImageFilter.UnsharpMask(radius=2, percent=200, threshold=3))
        enhanced_versions.append(('ultra_sharp', np.array(version1)))

        # Version 2: Extreme Contrast + Brightness
        version2 = pil_image.copy()
        version2 = ImageEnhance.Contrast(version2).enhance(3.0)
        version2 = ImageEnhance.Brightness(version2).enhance(1.3)
        enhanced_versions.append(('extreme_contrast', np.array(version2)))

        # Version 3: Detail Enhancement
        version3 = pil_image.copy()
        version3 = version3.filter(ImageFilter.DETAIL)
        version3 = ImageEnhance.Sharpness(version3).enhance(2.5)
        version3 = ImageEnhance.Contrast(version3).enhance(1.8)
        enhanced_versions.append(('detail_enhanced', np.array(version3)))

        # Version 4: Edge Enhancement
        version4 = pil_image.copy()
        version4 = version4.filter(ImageFilter.EDGE_ENHANCE_MORE)
        version4 = ImageEnhance.Contrast(version4).enhance(2.0)
        enhanced_versions.append(('edge_enhanced', np.array(version4)))

        return enhanced_versions

    def advanced_preprocessing(self, image: np.ndarray) -> List[Tuple[str, np.ndarray]]:
        """
        Tiền xử lý ảnh nâng cao với nhiều kỹ thuật
        """
        processed_versions = []

        # Chuyển sang grayscale
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # Resize nếu ảnh quá nhỏ
        height, width = gray.shape
        if height < 1000:
            scale = 1000 / height
            new_width = int(width * scale)
            gray = cv2.resize(gray, (new_width, 1000), interpolation=cv2.INTER_CUBIC)

        # Version 1: CLAHE + Bilateral Filter
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        clahe_img = clahe.apply(gray)
        bilateral = cv2.bilateralFilter(clahe_img, 9, 75, 75)
        _, binary1 = cv2.threshold(bilateral, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        processed_versions.append(('clahe_bilateral', binary1))

        # Version 2: Adaptive Gaussian + Morphology
        adaptive = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 15, 10)
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (2, 2))
        morph = cv2.morphologyEx(adaptive, cv2.MORPH_CLOSE, kernel)
        processed_versions.append(('adaptive_morph', morph))

        # Version 3: Multi-scale Retinex
        retinex = self.multi_scale_retinex(gray)
        _, binary3 = cv2.threshold(retinex, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        processed_versions.append(('retinex', binary3))

        # Version 4: Wiener Filter + Sharpening
        wiener = self.wiener_filter(gray)
        sharpened = self.unsharp_mask(wiener)
        _, binary4 = cv2.threshold(sharpened, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        processed_versions.append(('wiener_sharp', binary4))

        return processed_versions

    def multi_scale_retinex(self, image: np.ndarray) -> np.ndarray:
        """
        Multi-scale Retinex để cải thiện độ tương phản
        """
        try:
            img = image.astype(np.float64) + 1.0

            # Các scale khác nhau
            scales = [15, 80, 250]
            weights = [1/3, 1/3, 1/3]

            msr = np.zeros_like(img)

            for i, scale in enumerate(scales):
                # Gaussian blur
                blurred = cv2.GaussianBlur(img, (0, 0), scale)
                # Log transform
                log_img = np.log(img)
                log_blurred = np.log(blurred)
                # Retinex
                retinex = log_img - log_blurred
                msr += weights[i] * retinex

            # Normalize
            msr = (msr - msr.min()) / (msr.max() - msr.min()) * 255
            return msr.astype(np.uint8)

        except Exception as e:
            logger.warning(f"Multi-scale Retinex failed: {e}")
            return image

    def wiener_filter(self, image: np.ndarray, noise_var: float = 0.01) -> np.ndarray:
        """
        Wiener filter để giảm noise
        """
        try:
            # FFT
            f_transform = np.fft.fft2(image)
            f_shift = np.fft.fftshift(f_transform)

            # Power spectral density
            psd = np.abs(f_shift) ** 2

            # Wiener filter
            wiener = psd / (psd + noise_var)

            # Apply filter
            filtered = f_shift * wiener

            # Inverse FFT
            f_ishift = np.fft.ifftshift(filtered)
            img_back = np.fft.ifft2(f_ishift)
            img_back = np.abs(img_back)

            # Normalize
            img_back = (img_back - img_back.min()) / (img_back.max() - img_back.min()) * 255
            return img_back.astype(np.uint8)

        except Exception as e:
            logger.warning(f"Wiener filter failed: {e}")
            return image

    def unsharp_mask(self, image: np.ndarray, sigma: float = 1.0, strength: float = 1.5) -> np.ndarray:
        """
        Unsharp masking để tăng độ sắc nét
        """
        try:
            # Gaussian blur
            blurred = cv2.GaussianBlur(image, (0, 0), sigma)

            # Unsharp mask
            sharpened = cv2.addWeighted(image, 1 + strength, blurred, -strength, 0)

            return np.clip(sharpened, 0, 255).astype(np.uint8)

        except Exception as e:
            logger.warning(f"Unsharp mask failed: {e}")
            return image

    def extract_text_tesseract_optimized(self, image: np.ndarray) -> Dict:
        """
        Tesseract OCR tối ưu với nhiều config
        """
        best_result = {'text': '', 'confidence': 0, 'config': ''}

        for config in self.tesseract_configs:
            try:
                # Trích xuất text với confidence
                data = pytesseract.image_to_data(image, config=config, output_type=pytesseract.Output.DICT)

                # Tính confidence trung bình
                confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
                avg_confidence = sum(confidences) / len(confidences) if confidences else 0

                # Lấy text
                text = pytesseract.image_to_string(image, config=config).strip()

                # Đánh giá kết quả
                score = len(text) * 0.7 + avg_confidence * 0.3
                best_score = len(best_result['text']) * 0.7 + best_result['confidence'] * 0.3

                if score > best_score:
                    best_result = {
                        'text': text,
                        'confidence': avg_confidence,
                        'config': config
                    }

            except Exception as e:
                logger.warning(f"Tesseract config {config} failed: {e}")
                continue

        return best_result

    def extract_text_easyocr_optimized(self, image: np.ndarray) -> Dict:
        """
        EasyOCR tối ưu với GPU
        """
        try:
            if self.easyocr_reader is None:
                return {'text': '', 'confidence': 0, 'error': 'EasyOCR not available'}

            # Sử dụng confidence threshold thấp hơn để lấy nhiều text hơn
            results = self.easyocr_reader.readtext(image, detail=1, paragraph=False)

            text_lines = []
            total_confidence = 0
            count = 0

            for (bbox, text, confidence) in results:
                if confidence > 0.2:  # Threshold thấp hơn
                    text_lines.append(text)
                    total_confidence += confidence
                    count += 1

            avg_confidence = (total_confidence / count * 100) if count > 0 else 0

            return {
                'text': '\n'.join(text_lines),
                'confidence': avg_confidence
            }

        except Exception as e:
            logger.error(f"EasyOCR optimized error: {e}")
            return {'text': '', 'confidence': 0, 'error': str(e)}

    def process_prescription_gpu_optimized(self, image: np.ndarray) -> Dict:
        """
        Xử lý đơn thuốc tối ưu GPU với độ chính xác cao nhất
        """
        logger.info(f"Processing with GPU: {self.use_gpu}")

        # 1. Tạo nhiều phiên bản ảnh enhanced
        enhanced_versions = self.ultra_enhance_image(image)

        # 2. Tạo nhiều phiên bản preprocessed
        processed_versions = self.advanced_preprocessing(image)

        # 3. Kết hợp tất cả phiên bản
        all_versions = enhanced_versions + processed_versions

        best_tesseract = {'text': '', 'confidence': 0, 'version': ''}
        best_easyocr = {'text': '', 'confidence': 0, 'version': ''}

        # 4. Test OCR trên tất cả phiên bản
        for version_name, processed_image in all_versions:
            try:
                # Tesseract
                tesseract_result = self.extract_text_tesseract_optimized(processed_image)
                tesseract_score = len(tesseract_result['text']) * 0.7 + tesseract_result['confidence'] * 0.3
                best_tesseract_score = len(best_tesseract['text']) * 0.7 + best_tesseract['confidence'] * 0.3

                if tesseract_score > best_tesseract_score:
                    best_tesseract = tesseract_result.copy()
                    best_tesseract['version'] = version_name

                # EasyOCR (test trên ảnh màu nếu có)
                if len(processed_image.shape) == 3 or version_name in ['ultra_sharp', 'extreme_contrast']:
                    easyocr_result = self.extract_text_easyocr_optimized(processed_image)
                    easyocr_score = len(easyocr_result['text']) * 0.7 + easyocr_result['confidence'] * 0.3
                    best_easyocr_score = len(best_easyocr['text']) * 0.7 + best_easyocr['confidence'] * 0.3

                    if easyocr_score > best_easyocr_score:
                        best_easyocr = easyocr_result.copy()
                        best_easyocr['version'] = version_name

            except Exception as e:
                logger.warning(f"OCR failed for version {version_name}: {e}")
                continue

        # 5. Kết hợp kết quả thông minh
        combined_text = self.smart_combine_results(best_tesseract['text'], best_easyocr['text'])

        # 6. Trích xuất thông tin đơn thuốc
        prescription_info = self.extract_prescription_info_advanced(combined_text)

        return {
            'tesseract_result': best_tesseract['text'],
            'easyocr_result': best_easyocr['text'],
            'combined_text': combined_text,
            'prescription_info': prescription_info,
            'processing_info': {
                'gpu_used': self.use_gpu,
                'tesseract_confidence': best_tesseract['confidence'],
                'tesseract_version': best_tesseract['version'],
                'tesseract_config': best_tesseract.get('config', ''),
                'easyocr_confidence': best_easyocr['confidence'],
                'easyocr_version': best_easyocr['version'],
                'total_versions_tested': len(all_versions)
            }
        }

    def smart_combine_results(self, text1: str, text2: str) -> str:
        """
        Kết hợp thông minh kết quả từ 2 OCR
        """
        if not text1 and not text2:
            return ""
        if not text1:
            return text2
        if not text2:
            return text1

        # Tách thành từ
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())

        # Kết hợp unique words
        all_words = words1.union(words2)

        # Ưu tiên text dài hơn làm base
        base_text = text1 if len(text1) > len(text2) else text2
        other_text = text2 if len(text1) > len(text2) else text1

        # Thêm các từ unique từ text khác
        result_lines = base_text.split('\n')
        other_lines = other_text.split('\n')

        for line in other_lines:
            line = line.strip()
            if line and not any(line.lower() in existing.lower() for existing in result_lines):
                result_lines.append(line)

        return '\n'.join(result_lines)

    def extract_prescription_info_advanced(self, text: str) -> Dict:
        """
        Trích xuất thông tin đơn thuốc nâng cao sử dụng Vietnamese patterns
        """
        # Sử dụng Vietnamese medicine extractor
        medicines_detailed = self.medicine_extractor.extract_medicines_advanced(text)
        dosage_instructions = self.medicine_extractor.extract_dosage_instructions(text)
        patient_info = self.medicine_extractor.extract_patient_info_advanced(text)

        # Trích xuất thông tin khác
        info = {
            'medicines': [med['name'] for med in medicines_detailed[:10]],  # Top 10 medicines
            'medicines_detailed': medicines_detailed,
            'dosage_instructions': dosage_instructions,
            'doctor_name': '',
            'patient_name': patient_info.get('patient_name', ''),
            'patient_age': patient_info.get('age', ''),
            'patient_gender': patient_info.get('gender', ''),
            'date': '',
            'hospital': '',
            'phone': patient_info.get('phone', ''),
            'diagnosis': '',
            'address': patient_info.get('address', '')
        }

        lines = [line.strip() for line in text.split('\n') if line.strip()]

        # Patterns cải thiện cho thông tin khác
        doctor_patterns = [
            r'(?i)(?:bác sĩ|doctor|dr\.?|bs\.?|thầy thuốc)\s*:?\s*([A-ZÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ][a-zàáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ\s]+)',
            r'(?i)(?:^|\s)(BS\.?\s+[A-ZÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ][a-zàáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ\s]+)',
            r'(?i)(?:^|\s)(Dr\.?\s+[A-ZÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ][a-zàáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ\s]+)',
        ]

        hospital_patterns = [
            r'(?i)(?:bệnh viện|hospital|phòng khám|clinic|trung tâm y tế|medical center)\s*:?\s*([A-ZÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ][a-zàáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ\s]+)',
            r'(?i)(bệnh viện\s+[A-ZÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ][a-zàáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ\s]+)',
            r'(?i)(BVĐK|BV|hospital)\s+([A-ZÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ][a-zàáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ\s]+)'
        ]

        date_patterns = [
            r'(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',
            r'(?i)(?:ngày|date|dated)\s*:?\s*(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',
            r'(\d{1,2}\s+(?:tháng|th)\s+\d{1,2}\s+(?:năm|year)\s+\d{2,4})',
            r'(?i)(?:ngày|date)\s*:?\s*(\d{1,2})\s*[/-]\s*(\d{1,2})\s*[/-]\s*(\d{2,4})'
        ]

        diagnosis_patterns = [
            r'(?i)(?:chẩn đoán|diagnosis|disease)\s*:?\s*([A-Za-zàáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ\s]+)',
            r'(?i)(?:bệnh|illness)\s*:?\s*([A-Za-zàáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ\s]+)'
        ]

        # Xử lý từng dòng
        for line in lines:
            line_original = line
            line_lower = line.lower()

            # Tìm bác sĩ
            if not info['doctor_name']:
                for pattern in doctor_patterns:
                    match = re.search(pattern, line)
                    if match:
                        doctor_name = match.group(1).strip()
                        if len(doctor_name) > 2 and len(doctor_name.split()) >= 2:
                            info['doctor_name'] = doctor_name
                            break

            # Tìm bệnh viện
            if not info['hospital']:
                for pattern in hospital_patterns:
                    match = re.search(pattern, line)
                    if match:
                        hospital = match.group(1).strip() if len(match.groups()) == 1 else match.group(2).strip()
                        if len(hospital) > 3:
                            info['hospital'] = hospital
                            break

            # Tìm ngày
            if not info['date']:
                for pattern in date_patterns:
                    match = re.search(pattern, line)
                    if match:
                        if len(match.groups()) == 1:
                            info['date'] = match.group(1).strip()
                        else:
                            # Kết hợp các nhóm
                            date_parts = [g for g in match.groups() if g]
                            info['date'] = '/'.join(date_parts)
                        break

            # Tìm chẩn đoán
            if not info['diagnosis']:
                for pattern in diagnosis_patterns:
                    match = re.search(pattern, line)
                    if match:
                        diagnosis = match.group(1).strip()
                        if len(diagnosis) > 3:
                            info['diagnosis'] = diagnosis
                            break

        return info
