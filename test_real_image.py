#!/usr/bin/env python3
"""
Script test OCR trên ảnh thực tế từ folder img
"""

import requests
import json
import time
import os
from PIL import Image
import glob

def test_image_with_api(image_path):
    """
    Test một ảnh với API OCR
    """
    print(f"\n🔍 Testing: {os.path.basename(image_path)}")
    print("-" * 50)

    try:
        # Đọc ảnh
        with open(image_path, 'rb') as f:
            files = {'file': (os.path.basename(image_path), f, 'image/jpeg')}

            # Gửi request đến API
            start_time = time.time()
            response = requests.post('http://localhost:8000/extract-text', files=files, timeout=120)
            end_time = time.time()

            if response.status_code == 200:
                result = response.json()

                print(f"✅ Xử lý thành công trong {end_time - start_time:.2f} giây")

                # Hiển thị kết quả
                display_results(result)

                return result
            else:
                print(f"❌ API Error: {response.status_code}")
                print(response.text)
                return None

    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return None

def display_results(result):
    """
    Hiển thị kết quả OCR một cách dễ đọc
    """
    print("\n📝 KẾT QUẢ TESSERACT:")
    tesseract_text = result.get('tesseract_result', '')
    if tesseract_text:
        print(f"Độ dài: {len(tesseract_text)} ký tự")
        print("Text:")
        print(tesseract_text[:500] + "..." if len(tesseract_text) > 500 else tesseract_text)
    else:
        print("Không có kết quả")

    print("\n👁️ KẾT QUẢ EASYOCR:")
    easyocr_text = result.get('easyocr_result', '')
    if easyocr_text:
        print(f"Độ dài: {len(easyocr_text)} ký tự")
        print("Text:")
        print(easyocr_text[:500] + "..." if len(easyocr_text) > 500 else easyocr_text)
    else:
        print("Không có kết quả")

    print("\n💊 THÔNG TIN ĐƠN THUỐC:")
    prescription_info = result.get('prescription_info', {})

    patient_name = prescription_info.get('patient_name', '')
    if patient_name:
        print(f"👤 Bệnh nhân: {patient_name}")

    doctor_name = prescription_info.get('doctor_name', '')
    if doctor_name:
        print(f"👨‍⚕️ Bác sĩ: {doctor_name}")

    date = prescription_info.get('date', '')
    if date:
        print(f"📅 Ngày: {date}")

    hospital = prescription_info.get('hospital', '')
    if hospital:
        print(f"🏥 Bệnh viện: {hospital}")

    phone = prescription_info.get('phone', '')
    if phone:
        print(f"📞 Điện thoại: {phone}")

    medicines = prescription_info.get('medicines', [])
    if medicines:
        print(f"💊 Thuốc ({len(medicines)} loại):")
        for i, medicine in enumerate(medicines[:10], 1):  # Hiển thị tối đa 10 thuốc
            print(f"   {i}. {medicine}")

    dosage_instructions = prescription_info.get('dosage_instructions', [])
    if dosage_instructions:
        print(f"📋 Hướng dẫn sử dụng ({len(dosage_instructions)} mục):")
        for i, instruction in enumerate(dosage_instructions[:5], 1):  # Hiển thị tối đa 5 hướng dẫn
            print(f"   {i}. {instruction}")

    # Thông tin xử lý
    processing_info = result.get('processing_info', {})
    if processing_info:
        print(f"\n⚙️ THÔNG TIN XỬ LÝ:")
        tesseract_version = processing_info.get('tesseract_version_used', 'unknown')
        tesseract_config = processing_info.get('tesseract_config_used', 'unknown')
        easyocr_version = processing_info.get('easyocr_version_used', 'unknown')
        total_versions = processing_info.get('total_versions_tested', 0)
        skew_angle = processing_info.get('skew_angle', 0)
        advanced_processing = processing_info.get('advanced_processing', 'unknown')

        print(f"   Tesseract version: {tesseract_version}")
        print(f"   Tesseract config: {tesseract_config}")
        print(f"   EasyOCR version: {easyocr_version}")
        print(f"   Số phiên bản test: {total_versions}")
        print(f"   Góc nghiêng: {skew_angle:.2f}°")
        print(f"   Advanced processing: {advanced_processing}")

def save_results_to_file(image_path, result):
    """
    Lưu kết quả vào file text
    """
    if not result:
        return

    base_name = os.path.splitext(os.path.basename(image_path))[0]
    output_file = f"results_{base_name}.txt"

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"KẾT QUẢ OCR CHO: {os.path.basename(image_path)}\n")
        f.write("=" * 60 + "\n\n")

        f.write("TESSERACT RESULT:\n")
        f.write("-" * 30 + "\n")
        f.write(result.get('tesseract_result', '') + "\n\n")

        f.write("EASYOCR RESULT:\n")
        f.write("-" * 30 + "\n")
        f.write(result.get('easyocr_result', '') + "\n\n")

        f.write("COMBINED TEXT:\n")
        f.write("-" * 30 + "\n")
        f.write(result.get('combined_text', '') + "\n\n")

        f.write("PRESCRIPTION INFO:\n")
        f.write("-" * 30 + "\n")
        prescription_info = result.get('prescription_info', {})
        for key, value in prescription_info.items():
            if value:
                f.write(f"{key}: {value}\n")

    print(f"💾 Kết quả đã được lưu vào: {output_file}")

def main():
    print("🔍 TEST OCR TRÊN ẢNH THỰC TẾ")
    print("=" * 60)

    # Tìm tất cả ảnh trong folder img
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
    image_files = []

    for ext in image_extensions:
        image_files.extend(glob.glob(f"img/{ext}"))
        image_files.extend(glob.glob(f"img/{ext.upper()}"))

    if not image_files:
        print("❌ Không tìm thấy ảnh nào trong folder img/")
        return

    print(f"📁 Tìm thấy {len(image_files)} ảnh:")
    for img_file in image_files:
        print(f"   - {os.path.basename(img_file)}")

    # Kiểm tra API có hoạt động không
    try:
        response = requests.get('http://localhost:8000/health', timeout=5)
        if response.status_code != 200:
            print("❌ API không hoạt động. Hãy chạy: python run_api.py")
            return
        print("✅ API đang hoạt động")
    except:
        print("❌ Không thể kết nối API. Hãy chạy: python run_api.py")
        return

    # Test từng ảnh
    total_success = 0
    total_time = 0

    for image_path in image_files:
        try:
            # Kiểm tra ảnh có mở được không
            with Image.open(image_path) as img:
                print(f"📏 Kích thước ảnh: {img.size}")

            start_time = time.time()
            result = test_image_with_api(image_path)
            end_time = time.time()

            if result:
                total_success += 1
                total_time += (end_time - start_time)

                # Lưu kết quả
                save_results_to_file(image_path, result)

        except Exception as e:
            print(f"❌ Lỗi khi xử lý {image_path}: {e}")

    # Tổng kết
    print("\n" + "=" * 60)
    print("📊 TỔNG KẾT")
    print("=" * 60)
    print(f"Tổng số ảnh: {len(image_files)}")
    print(f"Xử lý thành công: {total_success}")
    print(f"Thời gian trung bình: {total_time/total_success:.2f} giây/ảnh" if total_success > 0 else "N/A")

    if total_success > 0:
        print("✅ Hoàn thành! Kiểm tra các file results_*.txt để xem kết quả chi tiết.")
    else:
        print("❌ Không có ảnh nào được xử lý thành công.")

if __name__ == "__main__":
    main()
