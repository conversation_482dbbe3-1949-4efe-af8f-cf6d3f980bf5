@echo off
echo ========================================
echo    PRESCRIPTION OCR - KHOI DONG
echo ========================================

REM Kich hoat virtual environment neu co
if exist "prescription_ocr_env\Scripts\activate.bat" (
    echo Kich hoat virtual environment...
    call prescription_ocr_env\Scripts\activate.bat
)

REM Kiem tra cai dat
echo Kiem tra cai dat...
python test_installation.py
if errorlevel 1 (
    echo.
    echo CAI DAT CHUA HOAN TAT! Vui long xem INSTALL.md
    pause
    exit /b 1
)

echo.
echo ========================================
echo Chon che do chay:
echo 1. Chi chay API Server (port 8000)
echo 2. Chi chay Streamlit UI (port 8501)  
echo 3. Chay ca hai (khu<PERSON>nn<PERSON><PERSON>)
echo ========================================
set /p choice="Nhap lua chon (1-3): "

if "%choice%"=="1" (
    echo Khoi dong API Server...
    python run_api.py
) else if "%choice%"=="2" (
    echo Khoi dong Streamlit UI...
    python run_ui.py
) else if "%choice%"=="3" (
    echo Khoi dong ca API va UI...
    start "API Server" cmd /k python run_api.py
    timeout /t 3 /nobreak >nul
    start "Streamlit UI" cmd /k python run_ui.py
    echo.
    echo Da khoi dong ca hai service!
    echo API: http://localhost:8000
    echo UI:  http://localhost:8501
) else (
    echo Lua chon khong hop le!
    pause
    exit /b 1
)

pause
