import cv2
import numpy as np
from PIL import Image
import pytesseract
import easyocr
import re
from typing import List, Tuple, Dict
import logging
from .image_preprocessing import PrescriptionImageProcessor
from .advanced_preprocessing import AdvancedPrescriptionProcessor

logger = logging.getLogger(__name__)

class OCRProcessor:
    """
    Class xử lý OCR chuyên biệt cho đơn thuốc
    """

    def __init__(self):
        try:
            self.easyocr_reader = easyocr.Reader(['vi', 'en'])
            logger.info("EasyOCR reader initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize EasyOCR: {e}")
            self.easyocr_reader = None

        # Khởi tạo image processors
        self.image_processor = PrescriptionImageProcessor()
        self.advanced_processor = AdvancedPrescriptionProcessor()

    def preprocess_prescription_image(self, image: np.ndarray) -> np.ndarray:
        """
        Tiền xử lý ảnh đơn thuốc để tối ưu OCR
        """
        # <PERSON><PERSON><PERSON><PERSON> sang grayscale nếu cần
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # Tăng contrast
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)

        # Giảm noise
        denoised = cv2.medianBlur(enhanced, 3)

        # Adaptive threshold để xử lý ánh sáng không đều
        binary = cv2.adaptiveThreshold(
            denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY, 11, 2
        )

        # Morphological operations để làm sạch
        kernel = np.ones((2, 2), np.uint8)
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

        return cleaned

    def extract_text_tesseract_multi_config(self, image: np.ndarray) -> Dict[str, any]:
        """
        Trích xuất text sử dụng Tesseract với nhiều cấu hình khác nhau
        """
        # Các cấu hình Tesseract khác nhau cho đơn thuốc
        # Thử tiếng Việt trước, fallback sang tiếng Anh
        configs = [
            '--oem 3 --psm 6 -l eng',      # Mixed text, uniform block (English only)
            '--oem 3 --psm 4 -l eng',      # Single column of text
            '--oem 3 --psm 3 -l eng',      # Fully automatic page segmentation
            '--oem 3 --psm 6 -l vie+eng',  # Mixed text với tiếng Việt (nếu có)
            '--oem 3 --psm 4 -l vie+eng',  # Single column với tiếng Việt
            '--oem 3 --psm 8 -l eng',      # Single word
            '--oem 3 --psm 7 -l eng',      # Single text line
            '--oem 1 --psm 6 -l eng',      # Neural nets LSTM engine
        ]

        best_result = {'text': '', 'words': [], 'confidence_avg': 0, 'config': ''}

        for config in configs:
            try:
                # Trích xuất text
                text = pytesseract.image_to_string(image, config=config)

                # Trích xuất thông tin chi tiết
                data = pytesseract.image_to_data(image, config=config, output_type=pytesseract.Output.DICT)

                # Lọc các từ có confidence cao
                words = []
                confidence_sum = 0
                confidence_count = 0

                for i in range(len(data['text'])):
                    if int(data['conf'][i]) > 30 and data['text'][i].strip():  # Confidence > 30% và không rỗng
                        word_info = {
                            'text': str(data['text'][i]).strip(),
                            'confidence': float(data['conf'][i]),
                            'bbox': (int(data['left'][i]), int(data['top'][i]),
                                    int(data['width'][i]), int(data['height'][i]))
                        }
                        words.append(word_info)
                        confidence_sum += float(data['conf'][i])
                        confidence_count += 1

                # Tính confidence trung bình
                avg_confidence = confidence_sum / confidence_count if confidence_count > 0 else 0

                # Đánh giá kết quả (độ dài text + confidence trung bình)
                text_length_score = len(text.strip())
                total_score = text_length_score * 0.7 + avg_confidence * 0.3

                if total_score > (len(best_result['text']) * 0.7 + best_result['confidence_avg'] * 0.3):
                    best_result = {
                        'text': text.strip(),
                        'words': words,
                        'confidence_avg': avg_confidence,
                        'config': config,
                        'method': 'tesseract'
                    }

            except Exception as e:
                logger.warning(f"Tesseract config {config} failed: {e}")
                continue

        return best_result if best_result['text'] else {'text': '', 'words': [], 'method': 'tesseract', 'error': 'All configs failed'}

    def extract_text_tesseract(self, image: np.ndarray) -> Dict[str, any]:
        """
        Wrapper cho extract_text_tesseract_multi_config
        """
        return self.extract_text_tesseract_multi_config(image)

    def extract_text_easyocr(self, image: np.ndarray) -> Dict[str, any]:
        """
        Trích xuất text sử dụng EasyOCR
        """
        try:
            if self.easyocr_reader is None:
                return {'text': '', 'words': [], 'method': 'easyocr', 'error': 'EasyOCR not available'}

            results = self.easyocr_reader.readtext(image)

            text_lines = []
            words = []

            for (bbox, text, confidence) in results:
                if confidence > 0.3:  # Confidence > 30%
                    text_lines.append(text)
                    word_info = {
                        'text': str(text),
                        'confidence': float(confidence * 100),  # Convert to percentage
                        'bbox': [[float(x), float(y)] for x, y in bbox]  # Convert numpy arrays to lists
                    }
                    words.append(word_info)

            return {
                'text': '\n'.join(text_lines),
                'words': words,
                'method': 'easyocr'
            }

        except Exception as e:
            logger.error(f"EasyOCR error: {e}")
            return {'text': '', 'words': [], 'method': 'easyocr', 'error': str(e)}

    def extract_prescription_info(self, text: str) -> Dict[str, any]:
        """
        Trích xuất thông tin cụ thể từ đơn thuốc với độ chính xác cao
        """
        info = {
            'medicines': [],
            'doctor_name': '',
            'patient_name': '',
            'date': '',
            'hospital': '',
            'dosage_instructions': [],
            'diagnosis': '',
            'phone': '',
            'address': ''
        }

        lines = [line.strip() for line in text.split('\n') if line.strip()]

        # Patterns nâng cao để tìm thông tin
        medicine_patterns = [
            r'(?i)(?:thuốc|medicine|drug|medication|tên thuốc)\s*:?\s*(.+)',
            r'(?i)(?:^|\s)([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s*(?:\d+mg|\d+ml|\d+g)',  # Tên thuốc + liều lượng
            r'(?i)(paracetamol|aspirin|ibuprofen|amoxicillin|cephalexin|metformin|insulin|vitamin)',  # Thuốc phổ biến
            r'(?i)(?:viên|tablet|capsule|ml|mg|g)\s+(.+)',  # Sau đơn vị
        ]

        doctor_patterns = [
            r'(?i)(?:bác sĩ|doctor|dr\.?|bs\.?|thầy thuốc)\s*:?\s*(.+)',
            r'(?i)(?:^|\s)(BS\.?\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',  # BS + tên
            r'(?i)(?:^|\s)(Dr\.?\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',  # Dr + tên
        ]

        patient_patterns = [
            r'(?i)(?:bệnh nhân|patient|tên|họ tên|name|bn)\s*:?\s*(.+)',
            r'(?i)(?:^|\s)([A-Z][a-z]+(?:\s+[A-Z][a-z]+){1,3})(?:\s+(?:nam|nữ|male|female))',  # Tên + giới tính
        ]

        date_patterns = [
            r'(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',
            r'(?i)(?:ngày|date|dated)\s*:?\s*(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',
            r'(\d{1,2}\s+(?:tháng|th)\s+\d{1,2}\s+(?:năm|year)\s+\d{2,4})',
        ]

        hospital_patterns = [
            r'(?i)(?:bệnh viện|hospital|phòng khám|clinic)\s*:?\s*(.+)',
            r'(?i)(bệnh viện\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
        ]

        phone_patterns = [
            r'(?:tel|phone|điện thoại|dt)\s*:?\s*([0-9\-\+\(\)\s]+)',
            r'(\+?84\s*[0-9\-\s]+)',
            r'(0[0-9\-\s]{8,})',
        ]

        dosage_patterns = [
            r'(?i)(?:uống|take|dùng|sử dụng)\s+(.+)',
            r'(?i)(\d+\s*(?:viên|tablet|ml|mg)\s*(?:mỗi|every|per)\s*(?:ngày|day|giờ|hour))',
            r'(?i)(sáng|chiều|tối|morning|afternoon|evening)\s*(\d+\s*(?:viên|tablet))',
        ]

        # Xử lý từng dòng
        for i, line in enumerate(lines):
            line_lower = line.lower()

            # Tìm thuốc
            for pattern in medicine_patterns:
                matches = re.findall(pattern, line)
                for match in matches:
                    medicine = match.strip() if isinstance(match, str) else match[0].strip()
                    if len(medicine) > 2 and medicine not in info['medicines']:
                        info['medicines'].append(medicine)

            # Tìm bác sĩ
            if not info['doctor_name']:
                for pattern in doctor_patterns:
                    match = re.search(pattern, line)
                    if match:
                        doctor_name = match.group(1).strip()
                        if len(doctor_name) > 2:
                            info['doctor_name'] = doctor_name
                            break

            # Tìm bệnh nhân
            if not info['patient_name']:
                for pattern in patient_patterns:
                    match = re.search(pattern, line)
                    if match:
                        patient_name = match.group(1).strip()
                        if len(patient_name) > 2:
                            info['patient_name'] = patient_name
                            break

            # Tìm ngày
            if not info['date']:
                for pattern in date_patterns:
                    match = re.search(pattern, line)
                    if match:
                        info['date'] = match.group(1).strip()
                        break

            # Tìm bệnh viện
            if not info['hospital']:
                for pattern in hospital_patterns:
                    match = re.search(pattern, line)
                    if match:
                        hospital = match.group(1).strip()
                        if len(hospital) > 3:
                            info['hospital'] = hospital
                            break

            # Tìm số điện thoại
            if not info['phone']:
                for pattern in phone_patterns:
                    match = re.search(pattern, line)
                    if match:
                        phone = re.sub(r'[^\d\+]', '', match.group(1))
                        if len(phone) >= 8:
                            info['phone'] = match.group(1).strip()
                            break

            # Tìm hướng dẫn sử dụng
            for pattern in dosage_patterns:
                matches = re.findall(pattern, line)
                for match in matches:
                    instruction = match.strip() if isinstance(match, str) else ' '.join(match).strip()
                    if len(instruction) > 3 and instruction not in info['dosage_instructions']:
                        info['dosage_instructions'].append(instruction)

        # Làm sạch kết quả
        info['medicines'] = [med for med in info['medicines'] if len(med) > 2 and not med.isdigit()]
        info['dosage_instructions'] = [inst for inst in info['dosage_instructions'] if len(inst) > 3]

        return info

    def process_prescription(self, image: np.ndarray) -> Dict[str, any]:
        """
        Xử lý hoàn chỉnh một ảnh đơn thuốc với pipeline nâng cao
        """
        results = {}

        # 1. Xử lý ảnh nâng cao
        advanced_results = self.advanced_processor.process_prescription_advanced(image)

        # 2. Tạo nhiều phiên bản xử lý từ basic processor
        basic_versions = self.image_processor.create_multiple_versions(image)

        # 3. Kết hợp tất cả phiên bản
        all_versions = basic_versions + [('advanced_final', advanced_results.get('final', image))]

        # Nếu có các bước trung gian từ advanced processor
        if 'enhanced' in advanced_results:
            all_versions.append(('advanced_enhanced', advanced_results['enhanced']))
        if 'denoised' in advanced_results:
            all_versions.append(('advanced_denoised', advanced_results['denoised']))
        if 'deskewed' in advanced_results:
            all_versions.append(('advanced_deskewed', advanced_results['deskewed']))

        best_tesseract_result = {'text': '', 'words': [], 'method': 'tesseract', 'confidence_avg': 0}
        best_easyocr_result = {'text': '', 'words': [], 'method': 'easyocr'}

        # 4. Thử OCR trên từng phiên bản
        for version_name, processed_image in all_versions:
            try:
                # Tesseract OCR với multi-config
                tesseract_result = self.extract_text_tesseract(processed_image)

                # Đánh giá kết quả Tesseract
                text_length = len(tesseract_result.get('text', ''))
                confidence = tesseract_result.get('confidence_avg', 0)
                tesseract_score = text_length * 0.7 + confidence * 0.3
                best_score = len(best_tesseract_result['text']) * 0.7 + best_tesseract_result.get('confidence_avg', 0) * 0.3

                if tesseract_score > best_score:
                    best_tesseract_result = tesseract_result
                    best_tesseract_result['version_used'] = version_name

                # EasyOCR (thử trên cả ảnh gốc và processed)
                if version_name in ['original', 'advanced_enhanced', 'advanced_final']:
                    easyocr_result = self.extract_text_easyocr(processed_image if len(processed_image.shape) == 3 else image)
                    if len(easyocr_result['text']) > len(best_easyocr_result['text']):
                        best_easyocr_result = easyocr_result
                        best_easyocr_result['version_used'] = version_name

            except Exception as e:
                logger.warning(f"OCR failed for version {version_name}: {e}")
                continue

        # 5. Kết hợp và làm sạch kết quả
        tesseract_text = best_tesseract_result.get('text', '')
        easyocr_text = best_easyocr_result.get('text', '')

        # Loại bỏ text trùng lặp
        combined_text = self.merge_ocr_results(tesseract_text, easyocr_text)

        # 6. Trích xuất thông tin đơn thuốc
        prescription_info = self.extract_prescription_info(combined_text)

        return {
            'tesseract_result': best_tesseract_result,
            'easyocr_result': best_easyocr_result,
            'prescription_info': prescription_info,
            'combined_text': combined_text,
            'processing_info': {
                'tesseract_version_used': best_tesseract_result.get('version_used', 'unknown'),
                'tesseract_config_used': best_tesseract_result.get('config', 'unknown'),
                'easyocr_version_used': best_easyocr_result.get('version_used', 'unknown'),
                'total_versions_tested': len(all_versions),
                'skew_angle': advanced_results.get('skew_angle', 0),
                'advanced_processing': 'success' if 'final' in advanced_results else 'failed'
            }
        }

    def merge_ocr_results(self, text1: str, text2: str) -> str:
        """
        Kết hợp kết quả từ 2 OCR engine, loại bỏ trùng lặp
        """
        if not text1 and not text2:
            return ""
        if not text1:
            return text2
        if not text2:
            return text1

        # Tách thành các dòng
        lines1 = [line.strip() for line in text1.split('\n') if line.strip()]
        lines2 = [line.strip() for line in text2.split('\n') if line.strip()]

        # Kết hợp và loại bỏ trùng lặp
        all_lines = lines1 + lines2
        unique_lines = []

        for line in all_lines:
            # Kiểm tra xem dòng này có tương tự với dòng nào đã có không
            is_duplicate = False
            for existing_line in unique_lines:
                # Tính độ tương tự (simple similarity)
                similarity = self.calculate_similarity(line, existing_line)
                if similarity > 0.8:  # 80% tương tự
                    is_duplicate = True
                    # Giữ dòng dài hơn
                    if len(line) > len(existing_line):
                        unique_lines[unique_lines.index(existing_line)] = line
                    break

            if not is_duplicate:
                unique_lines.append(line)

        return '\n'.join(unique_lines)

    def calculate_similarity(self, str1: str, str2: str) -> float:
        """
        Tính độ tương tự giữa 2 chuỗi
        """
        if not str1 or not str2:
            return 0.0

        # Chuyển về lowercase và loại bỏ khoảng trắng thừa
        s1 = ' '.join(str1.lower().split())
        s2 = ' '.join(str2.lower().split())

        if s1 == s2:
            return 1.0

        # Tính Jaccard similarity
        set1 = set(s1.split())
        set2 = set(s2.split())

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0
