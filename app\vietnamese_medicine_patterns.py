import re
from typing import List, Dict, Tuple
import logging

logger = logging.getLogger(__name__)

class VietnameseMedicineExtractor:
    """
    Class chuyên trích xuất thông tin thuốc từ đơn thuốc Việt Nam
    """
    
    def __init__(self):
        # <PERSON><PERSON> sách thuốc phổ biến tại Việt Nam
        self.common_medicines = [
            # Thu<PERSON>c gi<PERSON>au, hạ sốt
            'paracetamol', 'acetaminophen', 'aspirin', 'ibuprofen', 'diclofenac',
            'meloxicam', 'celecoxib', 'naproxen', 'ketoprofen',
            
            # Kháng sinh
            'amoxicillin', 'ampicillin', 'cephalexin', 'cefixime', 'azithromycin',
            'clarithromycin', 'erythromycin', 'doxycycline', 'tetracycline',
            'ciprofloxacin', 'levofloxacin', 'metronidazole', 'tinidazole',
            
            # Thuốc dạ dày
            'omeprazole', 'lansoprazole', 'esomeprazole', 'pantoprazole',
            'ranitidine', 'famotidine', 'domperidone', 'metoclopramide',
            
            # Thuốc tim mạch
            'amlodipine', 'nifedipine', 'atenolol', 'metoprolol', 'propranolol',
            'enalapril', 'lisinopril', 'losartan', 'valsartan', 'telmisartan',
            'simvastatin', 'atorvastatin', 'rosuvastatin',
            
            # Thuốc tiểu đường
            'metformin', 'glibenclamide', 'gliclazide', 'glimepiride',
            'insulin', 'acarbose',
            
            # Vitamin và khoáng chất
            'vitamin', 'calcium', 'iron', 'zinc', 'magnesium', 'folic acid',
            'vitamin b1', 'vitamin b6', 'vitamin b12', 'vitamin c', 'vitamin d',
            'vitamin e', 'vitamin k',
            
            # Thuốc khác
            'prednisolone', 'dexamethasone', 'hydrocortisone',
            'loratadine', 'cetirizine', 'chlorpheniramine',
            'salbutamol', 'theophylline', 'montelukast'
        ]
        
        # Đơn vị thuốc
        self.medicine_units = [
            'mg', 'ml', 'g', 'mcg', 'iu', 'viên', 'tablet', 'capsule',
            'cap', 'vỉ', 'lọ', 'chai', 'tuýp', 'ống', 'gói'
        ]
        
        # Từ khóa liều lượng
        self.dosage_keywords = [
            'lần', 'ngày', 'sáng', 'trưa', 'chiều', 'tối', 'khuya',
            'trước ăn', 'sau ăn', 'trong ăn', 'đói', 'no',
            'times', 'day', 'morning', 'noon', 'evening', 'night',
            'before meal', 'after meal', 'with meal'
        ]
    
    def extract_medicines_advanced(self, text: str) -> List[Dict]:
        """
        Trích xuất thuốc với thông tin chi tiết
        """
        medicines = []
        lines = text.split('\n')
        
        for line_num, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
            
            # Pattern 1: Tên thuốc + liều lượng
            pattern1 = r'(?i)([A-Za-z][A-Za-z\s]+?)\s*(\d+(?:\.\d+)?)\s*(mg|ml|g|mcg|iu|viên)'
            matches1 = re.findall(pattern1, line)
            
            for match in matches1:
                medicine_name = match[0].strip()
                dosage = match[1]
                unit = match[2]
                
                if self.is_valid_medicine_name(medicine_name):
                    medicine_info = {
                        'name': medicine_name,
                        'dosage': f"{dosage} {unit}",
                        'line_number': line_num + 1,
                        'full_line': line,
                        'confidence': self.calculate_medicine_confidence(medicine_name)
                    }
                    medicines.append(medicine_info)
            
            # Pattern 2: Thuốc phổ biến
            for common_med in self.common_medicines:
                if common_med.lower() in line.lower():
                    # Tìm liều lượng gần đó
                    dosage_pattern = r'(\d+(?:\.\d+)?)\s*(mg|ml|g|mcg|iu|viên)'
                    dosage_matches = re.findall(dosage_pattern, line)
                    
                    dosage_str = ""
                    if dosage_matches:
                        dosage_str = f"{dosage_matches[0][0]} {dosage_matches[0][1]}"
                    
                    medicine_info = {
                        'name': common_med.title(),
                        'dosage': dosage_str,
                        'line_number': line_num + 1,
                        'full_line': line,
                        'confidence': 0.9  # High confidence for common medicines
                    }
                    medicines.append(medicine_info)
            
            # Pattern 3: Số thứ tự + tên thuốc
            pattern3 = r'(?i)^\s*(\d+[\.\)])\s*([A-Za-z][A-Za-z\s]+?)(?:\s*[-:]?\s*(\d+(?:\.\d+)?)\s*(mg|ml|g|mcg|iu|viên))?'
            match3 = re.match(pattern3, line)
            
            if match3:
                medicine_name = match3.group(2).strip()
                dosage = match3.group(3) if match3.group(3) else ""
                unit = match3.group(4) if match3.group(4) else ""
                
                if self.is_valid_medicine_name(medicine_name):
                    medicine_info = {
                        'name': medicine_name,
                        'dosage': f"{dosage} {unit}".strip(),
                        'line_number': line_num + 1,
                        'full_line': line,
                        'confidence': self.calculate_medicine_confidence(medicine_name)
                    }
                    medicines.append(medicine_info)
        
        # Loại bỏ duplicate và sort theo confidence
        unique_medicines = self.remove_duplicate_medicines(medicines)
        return sorted(unique_medicines, key=lambda x: x['confidence'], reverse=True)
    
    def is_valid_medicine_name(self, name: str) -> bool:
        """
        Kiểm tra tên thuốc có hợp lệ không
        """
        name = name.strip().lower()
        
        # Loại bỏ các từ không phải tên thuốc
        invalid_words = [
            'thuốc', 'medicine', 'drug', 'medication', 'tên', 'name',
            'bệnh', 'nhân', 'patient', 'bác', 'sĩ', 'doctor', 'ngày', 'date',
            'viện', 'hospital', 'phòng', 'khám', 'clinic', 'địa', 'chỉ',
            'điện', 'thoại', 'phone', 'số', 'number', 'tuổi', 'age',
            'giới', 'tính', 'gender', 'nam', 'nữ', 'male', 'female'
        ]
        
        # Kiểm tra độ dài
        if len(name) < 3 or len(name) > 50:
            return False
        
        # Kiểm tra có chứa từ invalid không
        for invalid in invalid_words:
            if invalid in name:
                return False
        
        # Kiểm tra có ít nhất 1 chữ cái
        if not re.search(r'[a-zA-Z]', name):
            return False
        
        # Kiểm tra không phải toàn số
        if name.isdigit():
            return False
        
        return True
    
    def calculate_medicine_confidence(self, name: str) -> float:
        """
        Tính độ tin cậy của tên thuốc
        """
        name_lower = name.lower()
        
        # Thuốc phổ biến có confidence cao
        if name_lower in [med.lower() for med in self.common_medicines]:
            return 0.95
        
        # Tên có pattern thuốc (kết thúc bằng -in, -ol, -ide, etc.)
        medicine_suffixes = ['-in', '-ol', '-ide', '-ine', '-ate', '-ium']
        for suffix in medicine_suffixes:
            if name_lower.endswith(suffix.replace('-', '')):
                return 0.8
        
        # Tên có chứa vitamin
        if 'vitamin' in name_lower:
            return 0.9
        
        # Tên dài và có cấu trúc hợp lý
        if len(name) > 6 and re.match(r'^[A-Za-z][a-z]*[A-Za-z]$', name):
            return 0.7
        
        return 0.5
    
    def remove_duplicate_medicines(self, medicines: List[Dict]) -> List[Dict]:
        """
        Loại bỏ thuốc trùng lặp
        """
        unique_medicines = []
        seen_names = set()
        
        for medicine in medicines:
            name_lower = medicine['name'].lower()
            
            # Kiểm tra similarity với các thuốc đã có
            is_duplicate = False
            for seen_name in seen_names:
                if self.calculate_name_similarity(name_lower, seen_name) > 0.8:
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_medicines.append(medicine)
                seen_names.add(name_lower)
        
        return unique_medicines
    
    def calculate_name_similarity(self, name1: str, name2: str) -> float:
        """
        Tính độ tương tự giữa 2 tên thuốc
        """
        # Levenshtein distance đơn giản
        if name1 == name2:
            return 1.0
        
        # Kiểm tra substring
        if name1 in name2 or name2 in name1:
            return 0.9
        
        # Tính Jaccard similarity
        set1 = set(name1.split())
        set2 = set(name2.split())
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0.0
    
    def extract_dosage_instructions(self, text: str) -> List[str]:
        """
        Trích xuất hướng dẫn sử dụng thuốc
        """
        instructions = []
        lines = text.split('\n')
        
        instruction_patterns = [
            r'(?i)(?:uống|take|dùng|sử dụng|use)\s+(.+)',
            r'(?i)(\d+\s*(?:viên|tablet|ml|mg)\s*(?:mỗi|every|per)\s*(?:ngày|day|giờ|hour))',
            r'(?i)(sáng|chiều|tối|morning|afternoon|evening)\s*(\d+\s*(?:viên|tablet))',
            r'(?i)(?:trước|sau|before|after)\s*(?:ăn|meal)',
            r'(?i)(?:ngày|day)\s*(\d+)\s*(?:lần|times)',
            r'(?i)(\d+)\s*(?:lần|times)\s*(?:ngày|day)'
        ]
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            for pattern in instruction_patterns:
                matches = re.findall(pattern, line)
                for match in matches:
                    if isinstance(match, tuple):
                        instruction = ' '.join(match).strip()
                    else:
                        instruction = match.strip()
                    
                    if len(instruction) > 3 and instruction not in instructions:
                        instructions.append(instruction)
        
        return instructions[:10]  # Giới hạn 10 hướng dẫn
    
    def extract_patient_info_advanced(self, text: str) -> Dict:
        """
        Trích xuất thông tin bệnh nhân nâng cao
        """
        info = {
            'patient_name': '',
            'age': '',
            'gender': '',
            'address': '',
            'phone': '',
            'id_number': ''
        }
        
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Tên bệnh nhân
            if not info['patient_name']:
                name_patterns = [
                    r'(?i)(?:bệnh nhân|patient|tên|họ tên|name)\s*:?\s*([A-ZÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ][a-zàáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]+(?:\s+[A-ZÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ][a-zàáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]+)*)',
                ]
                
                for pattern in name_patterns:
                    match = re.search(pattern, line)
                    if match:
                        name = match.group(1).strip()
                        if len(name) > 2 and len(name.split()) >= 2:
                            info['patient_name'] = name
                            break
            
            # Tuổi
            if not info['age']:
                age_patterns = [
                    r'(?i)(?:tuổi|age)\s*:?\s*(\d{1,3})',
                    r'(?i)(\d{1,3})\s*(?:tuổi|years old)'
                ]
                
                for pattern in age_patterns:
                    match = re.search(pattern, line)
                    if match:
                        age = int(match.group(1))
                        if 0 <= age <= 120:
                            info['age'] = str(age)
                            break
            
            # Giới tính
            if not info['gender']:
                if re.search(r'(?i)\b(?:nam|male)\b', line):
                    info['gender'] = 'Nam'
                elif re.search(r'(?i)\b(?:nữ|female)\b', line):
                    info['gender'] = 'Nữ'
            
            # Số điện thoại
            if not info['phone']:
                phone_patterns = [
                    r'(?:tel|phone|điện thoại|dt|sđt)\s*:?\s*([0-9\-\+\(\)\s]{8,})',
                    r'(\+?84\s*[0-9\-\s]{8,})',
                    r'(0[0-9\-\s]{8,})'
                ]
                
                for pattern in phone_patterns:
                    match = re.search(pattern, line)
                    if match:
                        phone = re.sub(r'[^\d\+]', '', match.group(1))
                        if len(phone) >= 8:
                            info['phone'] = match.group(1).strip()
                            break
        
        return info
