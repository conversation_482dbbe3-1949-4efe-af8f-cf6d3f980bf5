#!/usr/bin/env python3
"""
Script test OCR đơn giản trên ảnh thực tế
"""

import requests
import json
import time
import os
from PIL import Image
import glob

def test_image_simple(image_path):
    """
    Test một ảnh với API OCR đơn giản
    """
    print(f"\n🔍 Testing: {os.path.basename(image_path)}")
    print("-" * 50)
    
    try:
        # Đ<PERSON>c ảnh
        with open(image_path, 'rb') as f:
            files = {'file': (os.path.basename(image_path), f, 'image/jpeg')}
            
            # Gửi request đến API simple
            start_time = time.time()
            response = requests.post('http://localhost:8000/extract-text-simple', files=files, timeout=60)
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"✅ Xử lý thành công trong {end_time - start_time:.2f} giây")
                
                # Hi<PERSON><PERSON> thị kết quả
                display_results_simple(result)
                
                return result
            else:
                print(f"❌ API Error: {response.status_code}")
                print(response.text)
                return None
                
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return None

def display_results_simple(result):
    """
    Hiển thị kết quả OCR đơn giản
    """
    print("\n📝 KẾT QUẢ TESSERACT:")
    tesseract_text = result.get('tesseract_result', '')
    if tesseract_text:
        print(f"Độ dài: {len(tesseract_text)} ký tự")
        print("Text:")
        # Hiển thị từng dòng để dễ đọc
        lines = tesseract_text.split('\n')
        for i, line in enumerate(lines[:20], 1):  # Chỉ hiển thị 20 dòng đầu
            if line.strip():
                print(f"  {i:2d}: {line}")
    else:
        print("Không có kết quả")
    
    print("\n👁️ KẾT QUẢ EASYOCR:")
    easyocr_text = result.get('easyocr_result', '')
    if easyocr_text:
        print(f"Độ dài: {len(easyocr_text)} ký tự")
        print("Text:")
        lines = easyocr_text.split('\n')
        for i, line in enumerate(lines[:20], 1):  # Chỉ hiển thị 20 dòng đầu
            if line.strip():
                print(f"  {i:2d}: {line}")
    else:
        print("Không có kết quả")
    
    print("\n💊 THÔNG TIN ĐƠN THUỐC:")
    prescription_info = result.get('prescription_info', {})
    
    patient_name = prescription_info.get('patient_name', '')
    if patient_name:
        print(f"👤 Bệnh nhân: {patient_name}")
    
    doctor_name = prescription_info.get('doctor_name', '')
    if doctor_name:
        print(f"👨‍⚕️ Bác sĩ: {doctor_name}")
    
    date = prescription_info.get('date', '')
    if date:
        print(f"📅 Ngày: {date}")
    
    hospital = prescription_info.get('hospital', '')
    if hospital:
        print(f"🏥 Bệnh viện: {hospital}")
    
    medicines = prescription_info.get('medicines', [])
    if medicines:
        print(f"💊 Thuốc ({len(medicines)} loại):")
        for i, medicine in enumerate(medicines[:10], 1):
            print(f"   {i}. {medicine}")
    
    # Thông tin xử lý
    processing_info = result.get('processing_info', {})
    if processing_info:
        print(f"\n⚙️ THÔNG TIN XỬ LÝ:")
        method = processing_info.get('method', 'unknown')
        tesseract_config = processing_info.get('tesseract_config', 'unknown')
        print(f"   Phương pháp: {method}")
        print(f"   Tesseract config: {tesseract_config}")

def compare_with_advanced(image_path):
    """
    So sánh kết quả giữa simple và advanced
    """
    print(f"\n🔄 So sánh Simple vs Advanced cho: {os.path.basename(image_path)}")
    print("=" * 60)
    
    # Test simple
    print("1️⃣ SIMPLE OCR:")
    start_simple = time.time()
    simple_result = test_image_simple(image_path)
    end_simple = time.time()
    simple_time = end_simple - start_simple
    
    # Test advanced
    print("\n2️⃣ ADVANCED OCR:")
    try:
        with open(image_path, 'rb') as f:
            files = {'file': (os.path.basename(image_path), f, 'image/jpeg')}
            
            start_advanced = time.time()
            response = requests.post('http://localhost:8000/extract-text', files=files, timeout=120)
            end_advanced = time.time()
            advanced_time = end_advanced - start_advanced
            
            if response.status_code == 200:
                advanced_result = response.json()
                print(f"✅ Advanced xử lý thành công trong {advanced_time:.2f} giây")
            else:
                print(f"❌ Advanced failed: {response.status_code}")
                advanced_result = None
                
    except Exception as e:
        print(f"❌ Advanced error: {e}")
        advanced_result = None
    
    # So sánh
    print(f"\n📊 SO SÁNH:")
    print(f"⏱️ Thời gian:")
    print(f"   Simple: {simple_time:.2f}s")
    if advanced_result:
        print(f"   Advanced: {advanced_time:.2f}s")
        print(f"   Tỷ lệ: Advanced chậm hơn {advanced_time/simple_time:.1f}x")
    
    if simple_result and advanced_result:
        simple_text_len = len(simple_result.get('tesseract_result', '') + simple_result.get('easyocr_result', ''))
        advanced_text_len = len(advanced_result.get('tesseract_result', '') + advanced_result.get('easyocr_result', ''))
        
        print(f"📝 Độ dài text:")
        print(f"   Simple: {simple_text_len} ký tự")
        print(f"   Advanced: {advanced_text_len} ký tự")
        
        if advanced_text_len > simple_text_len:
            print(f"   ✅ Advanced trích xuất nhiều hơn {advanced_text_len - simple_text_len} ký tự")
        elif simple_text_len > advanced_text_len:
            print(f"   ⚠️ Simple trích xuất nhiều hơn {simple_text_len - advanced_text_len} ký tự")
        else:
            print(f"   ➡️ Cả hai có độ dài tương đương")

def main():
    print("🔍 TEST SIMPLE OCR TRÊN ẢNH THỰC TẾ")
    print("=" * 60)
    
    # Tìm ảnh
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png']:
        image_files.extend(glob.glob(f"img/{ext}"))
        image_files.extend(glob.glob(f"img/{ext.upper()}"))
    
    # Loại bỏ duplicate
    image_files = list(set(image_files))
    
    if not image_files:
        print("❌ Không tìm thấy ảnh nào trong folder img/")
        return
    
    print(f"📁 Tìm thấy {len(image_files)} ảnh:")
    for img_file in image_files:
        print(f"   - {os.path.basename(img_file)}")
    
    # Kiểm tra API
    try:
        response = requests.get('http://localhost:8000/health', timeout=5)
        if response.status_code != 200:
            print("❌ API không hoạt động. Hãy chạy: python run_api.py")
            return
        print("✅ API đang hoạt động")
    except:
        print("❌ Không thể kết nối API. Hãy chạy: python run_api.py")
        return
    
    # Hỏi user muốn test gì
    print("\nChọn chế độ test:")
    print("1. Chỉ test Simple OCR (nhanh)")
    print("2. So sánh Simple vs Advanced (chậm)")
    
    choice = input("Nhập lựa chọn (1 hoặc 2): ").strip()
    
    for image_path in image_files:
        try:
            # Kiểm tra ảnh
            with Image.open(image_path) as img:
                print(f"\n📏 Ảnh {os.path.basename(image_path)}: {img.size}")
            
            if choice == "2":
                compare_with_advanced(image_path)
            else:
                test_image_simple(image_path)
                
        except Exception as e:
            print(f"❌ Lỗi khi xử lý {image_path}: {e}")
    
    print("\n✅ Hoàn thành test!")

if __name__ == "__main__":
    main()
