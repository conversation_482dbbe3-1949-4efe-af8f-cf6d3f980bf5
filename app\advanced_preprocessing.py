import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
from typing import List, Tuple, Dict
import logging
from scipy import ndimage
from skimage import restoration, filters, morphology, segmentation
from skimage.feature import canny
from skimage.transform import hough_line, hough_line_peaks

logger = logging.getLogger(__name__)

class AdvancedPrescriptionProcessor:
    """
    Class xử lý ảnh đơn thuốc nâng cao với nhiều kỹ thuật tiên tiến
    """

    def __init__(self):
        self.debug_mode = False

    def set_debug(self, debug: bool):
        """Bật/tắt debug mode để lưu ảnh trung gian"""
        self.debug_mode = debug

    def enhance_image_quality(self, image: np.ndarray) -> np.ndarray:
        """
        Nâng cao chất lượng ảnh bằng nhiều kỹ thuật
        """
        try:
            # <PERSON><PERSON><PERSON><PERSON> sang PIL để xử lý
            if len(image.shape) == 3:
                pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            else:
                pil_image = Image.fromarray(image)

            # 1. Tăng độ sắc nét
            sharpness_enhancer = ImageEnhance.Sharpness(pil_image)
            enhanced = sharpness_enhancer.enhance(1.8)

            # 2. Tăng contrast
            contrast_enhancer = ImageEnhance.Contrast(enhanced)
            enhanced = contrast_enhancer.enhance(1.4)

            # 3. Điều chỉnh brightness nhẹ
            brightness_enhancer = ImageEnhance.Brightness(enhanced)
            enhanced = brightness_enhancer.enhance(1.1)

            # 4. Áp dụng unsharp mask
            enhanced = enhanced.filter(ImageFilter.UnsharpMask(radius=1, percent=150, threshold=3))

            return np.array(enhanced)

        except Exception as e:
            logger.error(f"Error in enhance_image_quality: {e}")
            return image

    def advanced_noise_reduction(self, image: np.ndarray) -> np.ndarray:
        """
        Giảm noise nâng cao sử dụng nhiều kỹ thuật
        """
        try:
            # Chuyển sang grayscale nếu cần
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # 1. Non-local means denoising
            denoised = cv2.fastNlMeansDenoising(gray, None, 10, 7, 21)

            # 2. Bilateral filter để giữ edges
            bilateral = cv2.bilateralFilter(denoised, 9, 75, 75)

            # 3. Median filter để loại bỏ salt-pepper noise
            median = cv2.medianBlur(bilateral, 3)

            # 4. Gaussian blur nhẹ
            gaussian = cv2.GaussianBlur(median, (3, 3), 0.5)

            return gaussian

        except Exception as e:
            logger.error(f"Error in advanced_noise_reduction: {e}")
            return image if len(image.shape) == 2 else cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    def detect_and_correct_skew(self, image: np.ndarray) -> Tuple[np.ndarray, float]:
        """
        Phát hiện và sửa độ nghiêng của ảnh bằng Hough Transform
        """
        try:
            # Tìm edges
            edges = canny(image, sigma=1, low_threshold=50, high_threshold=150)

            # Hough line transform
            tested_angles = np.linspace(-np.pi/2, np.pi/2, 360, endpoint=False)
            h, theta, d = hough_line(edges, theta=tested_angles)

            # Tìm các đường thẳng mạnh nhất
            hough_peaks = hough_line_peaks(h, theta, d, num_peaks=20)

            # Tính góc nghiêng trung bình
            angles = []
            for _, angle, dist in zip(*hough_peaks):
                angle_deg = np.degrees(angle)
                # Chỉ lấy các góc gần horizontal
                if abs(angle_deg) < 45:
                    angles.append(angle_deg)

            if angles:
                skew_angle = np.median(angles)

                # Xoay ảnh
                if abs(skew_angle) > 0.5:  # Chỉ xoay nếu góc > 0.5 độ
                    center = tuple(np.array(image.shape[1::-1]) / 2)
                    rot_mat = cv2.getRotationMatrix2D(center, skew_angle, 1.0)
                    corrected = cv2.warpAffine(image, rot_mat, image.shape[1::-1],
                                             flags=cv2.INTER_CUBIC,
                                             borderMode=cv2.BORDER_REPLICATE)
                    return corrected, skew_angle

            return image, 0.0

        except Exception as e:
            logger.error(f"Error in detect_and_correct_skew: {e}")
            return image, 0.0

    def adaptive_binarization(self, image: np.ndarray) -> np.ndarray:
        """
        Binarization thích ứng với nhiều phương pháp
        """
        try:
            # Chuyển sang grayscale
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # Thử nhiều phương pháp và chọn tốt nhất
            methods = []

            # 1. Adaptive Gaussian
            try:
                adaptive_gaussian = cv2.adaptiveThreshold(
                    gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 15, 10)
                methods.append(("adaptive_gaussian", adaptive_gaussian))
            except:
                pass

            # 2. Adaptive Mean
            try:
                adaptive_mean = cv2.adaptiveThreshold(
                    gray, 255, cv2.ADAPTIVE_THRESH_MEAN_C, cv2.THRESH_BINARY, 15, 10)
                methods.append(("adaptive_mean", adaptive_mean))
            except:
                pass

            # 3. OTSU
            try:
                _, otsu = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
                methods.append(("otsu", otsu))
            except:
                pass

            # 4. Sauvola threshold (local adaptive)
            try:
                from skimage.filters import threshold_sauvola
                sauvola_thresh = threshold_sauvola(gray, window_size=25)
                sauvola_binary = (gray > sauvola_thresh).astype(np.uint8) * 255
                methods.append(("sauvola", sauvola_binary))
            except Exception as e:
                logger.warning(f"Sauvola threshold failed: {e}")
                pass

            # 5. Niblack threshold
            try:
                from skimage.filters import threshold_niblack
                niblack_thresh = threshold_niblack(gray, window_size=25, k=0.2)
                niblack_binary = (gray > niblack_thresh).astype(np.uint8) * 255
                methods.append(("niblack", niblack_binary))
            except Exception as e:
                logger.warning(f"Niblack threshold failed: {e}")
                pass

            if not methods:
                # Fallback: simple threshold
                _, simple = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
                return simple

            # Đánh giá chất lượng của từng phương pháp
            best_method = None
            best_score = -1

            for name, binary_img in methods:
                # Tính điểm dựa trên tỷ lệ pixel trắng và độ liên tục
                white_ratio = np.sum(binary_img == 255) / binary_img.size

                # Tỷ lệ pixel trắng lý tưởng cho text: 0.1-0.3
                white_score = 1 - abs(white_ratio - 0.2) * 5
                white_score = max(0, white_score)

                # Tính độ liên tục của text (ít fragment)
                contours, _ = cv2.findContours(binary_img, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                continuity_score = 1 / (1 + len(contours) / 100)  # Ít contour = tốt hơn

                total_score = white_score * 0.7 + continuity_score * 0.3

                if total_score > best_score:
                    best_score = total_score
                    best_method = binary_img

            return best_method if best_method is not None else methods[0][1]

        except Exception as e:
            logger.error(f"Error in adaptive_binarization: {e}")
            _, fallback = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
            return fallback

    def morphological_enhancement(self, binary_image: np.ndarray) -> np.ndarray:
        """
        Cải thiện ảnh binary bằng morphological operations
        """
        try:
            # 1. Closing để nối các phần text bị đứt
            kernel_close = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (2, 2))
            closed = cv2.morphologyEx(binary_image, cv2.MORPH_CLOSE, kernel_close)

            # 2. Opening để loại bỏ noise nhỏ
            kernel_open = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (1, 1))
            opened = cv2.morphologyEx(closed, cv2.MORPH_OPEN, kernel_open)

            # 3. Dilation nhẹ để làm dày text
            kernel_dilate = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (1, 1))
            dilated = cv2.dilate(opened, kernel_dilate, iterations=1)

            return dilated

        except Exception as e:
            logger.error(f"Error in morphological_enhancement: {e}")
            return binary_image

    def process_prescription_advanced(self, image: np.ndarray) -> Dict[str, np.ndarray]:
        """
        Xử lý ảnh đơn thuốc với pipeline nâng cao
        """
        results = {}

        try:
            # 1. Resize ảnh nếu quá nhỏ
            height, width = image.shape[:2]
            if height < 800:
                scale = 800 / height
                new_width = int(width * scale)
                image = cv2.resize(image, (new_width, 800), interpolation=cv2.INTER_CUBIC)

            results['resized'] = image.copy()

            # 2. Nâng cao chất lượng ảnh
            enhanced = self.enhance_image_quality(image)
            results['enhanced'] = enhanced.copy()

            # 3. Giảm noise nâng cao
            denoised = self.advanced_noise_reduction(enhanced)
            results['denoised'] = denoised.copy()

            # 4. Sửa độ nghiêng
            deskewed, skew_angle = self.detect_and_correct_skew(denoised)
            results['deskewed'] = deskewed.copy()
            results['skew_angle'] = skew_angle

            # 5. Binarization thích ứng
            binary = self.adaptive_binarization(deskewed)
            results['binary'] = binary.copy()

            # 6. Morphological enhancement
            final = self.morphological_enhancement(binary)
            results['final'] = final.copy()

            return results

        except Exception as e:
            logger.error(f"Error in process_prescription_advanced: {e}")
            # Fallback: xử lý đơn giản
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
            return {'final': binary, 'error': str(e)}
